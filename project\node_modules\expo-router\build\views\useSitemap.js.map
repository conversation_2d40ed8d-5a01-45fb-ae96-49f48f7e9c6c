{"version": 3, "file": "useSitemap.js", "sourceRoot": "", "sources": ["../../src/views/useSitemap.tsx"], "names": [], "mappings": ";;AA8DA,gCAMC;AApED,iCAAgC;AAEhC,oCAAiD;AACjD,+DAAqD;AACrD,0CAA+C;AAG/C,MAAM,aAAa,GAAG,CAAC,KAAgB,EAAE,OAAiB,EAAE,EAAE,CAAC;IAC7D,GAAG,OAAO;IACV,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC;CAC1B,CAAC;AAEF,MAAM,SAAS,GAAG,CAAC,KAAgB,EAAE,OAAiB,EAAE,EAAE,CACxD,GAAG;IACH,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC;SAC1B,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;QACf,mEAAmE;QACnE,IAAI,IAAA,2BAAgB,EAAC,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC;YACpC,OAAO,OAAO,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACpC,CAAC;QACD,oDAAoD;QACpD,OAAO,OAAO,KAAK,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;IAC5C,CAAC,CAAC;SACD,MAAM,CAAC,OAAO,CAAC;SACf,IAAI,CAAC,GAAG,CAAC,CAAC;AAEf,MAAM,aAAa,GAAG,CAAC,KAAgB,EAAE,EAAE;IACzC,MAAM,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC7C,2CAA2C;IAC3C,IAAI,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,mBAAmB,CAAC,EAAE,CAAC;QAChD,OAAO,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC7E,CAAC;IAED,MAAM,kBAAkB,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;IAEzD,0CAA0C;IAC1C,oFAAoF;IACpF,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACvD,CAAC,CAAC;AAYF,MAAM,WAAW,GAAyD,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;IAC7F,UAAU,EAAE,KAAK,CAAC,UAAU;IAC5B,QAAQ,EAAE,aAAa,CAAC,KAAK,CAAC;IAC9B,IAAI,EAAE,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC;IAC/B,SAAS,EAAE,KAAK,CAAC,gBAAgB,KAAK,KAAK,CAAC,KAAK;IACjD,UAAU,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK;IACnC,WAAW,EAAE,KAAK,CAAC,SAAS,IAAI,KAAK;IACrC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC;SAC1B,IAAI,CAAC,kBAAU,CAAC;SAChB,GAAG,CAAC,CAAC,KAAgB,EAAE,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;CAChF,CAAC,CAAC;AAEH,SAAgB,UAAU;IACxB,MAAM,OAAO,GAAG,IAAA,eAAO,EACrB,GAAG,EAAE,CAAC,CAAC,oBAAK,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,oBAAK,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EACjE,CAAC,oBAAK,CAAC,SAAS,CAAC,CAClB,CAAC;IACF,OAAO,OAAO,CAAC;AACjB,CAAC", "sourcesContent": ["import { useMemo } from 'react';\n\nimport { RouteNode, sortRoutes } from '../Route';\nimport { store } from '../global-state/router-store';\nimport { matchDynamicName } from '../matchers';\nimport { Href } from '../types';\n\nconst routeSegments = (route: RouteNode, parents: string[]) => [\n  ...parents,\n  ...route.route.split('/'),\n];\n\nconst routeHref = (route: RouteNode, parents: string[]) =>\n  '/' +\n  routeSegments(route, parents)\n    .map((segment) => {\n      // add an extra layer of entropy to the url for deep dynamic routes\n      if (matchDynamicName(segment)?.deep) {\n        return segment + '/' + Date.now();\n      }\n      // index must be erased but groups can be preserved.\n      return segment === 'index' ? '' : segment;\n    })\n    .filter(Boolean)\n    .join('/');\n\nconst routeFilename = (route: RouteNode) => {\n  const segments = route.contextKey.split('/');\n  // join last two segments for layout routes\n  if (route.contextKey.match(/_layout\\.[jt]sx?$/)) {\n    return segments[segments.length - 2] + '/' + segments[segments.length - 1];\n  }\n\n  const routeSegmentsCount = route.route.split('/').length;\n\n  // Join the segment count in reverse order\n  // This presents files without layout routes as children with all relevant segments.\n  return segments.slice(-routeSegmentsCount).join('/');\n};\n\nexport type SitemapType = {\n  contextKey: string;\n  filename: string;\n  href: string | Href;\n  isInitial: boolean;\n  isInternal: boolean;\n  isGenerated: boolean;\n  children: SitemapType[];\n};\n\nconst mapForRoute: (route: RouteNode, parents: string[]) => SitemapType = (route, parents) => ({\n  contextKey: route.contextKey,\n  filename: routeFilename(route),\n  href: routeHref(route, parents),\n  isInitial: route.initialRouteName === route.route,\n  isInternal: route.internal ?? false,\n  isGenerated: route.generated ?? false,\n  children: [...route.children]\n    .sort(sortRoutes)\n    .map((child: RouteNode) => mapForRoute(child, routeSegments(route, parents))),\n});\n\nexport function useSitemap(): SitemapType | null {\n  const sitemap = useMemo(\n    () => (store.routeNode ? mapForRoute(store.routeNode, []) : null),\n    [store.routeNode]\n  );\n  return sitemap;\n}\n"]}