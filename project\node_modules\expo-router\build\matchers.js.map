{"version": 3, "file": "matchers.js", "sourceRoot": "", "sources": ["../src/matchers.tsx"], "names": [], "mappings": ";;AASA,4CASC;AAGD,oCAEC;AAGD,wCAEC;AAGD,gDAEC;AAGD,kDAEC;AAED,kDAEC;AAED,sCAQC;AAGD,8DAEC;AAGD,gEAEC;AAGD,oDAEC;AAED,gEAUC;AAED,wEAEC;AAOD,oCAEC;AA5FD,6DAA6D;AAC7D,MAAM,aAAa,GAAG,kBAAkB,CAAC;AAOzC,+BAA+B;AAC/B,SAAgB,gBAAgB,CAAC,IAAY;IAC3C,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjD,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;QACtB,OAAO,SAAS,CAAC;IACnB,CAAC;SAAM,IAAI,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;QACvC,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IAClD,CAAC;SAAM,CAAC;QACN,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;IAC1C,CAAC;AACH,CAAC;AAED,yBAAyB;AACzB,SAAgB,YAAY,CAAC,IAAY;IACvC,OAAO,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACnC,CAAC;AAED,+BAA+B;AAC/B,SAAgB,cAAc,CAAC,IAAY;IACzC,OAAO,IAAI,CAAC,KAAK,CAAC,6BAA6B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACxD,CAAC;AAED,qCAAqC;AACrC,SAAgB,kBAAkB,CAAC,IAAY;IAC7C,OAAO,IAAI,CAAC,KAAK,CAAC,gCAAgC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC;AAED,oEAAoE;AACpE,SAAgB,mBAAmB,CAAC,IAAY;IAC9C,OAAO,IAAI,CAAC,KAAK,CAAC,oCAAoC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC/D,CAAC;AAED,SAAgB,mBAAmB,CAAC,IAAY;IAC9C,OAAO,yBAAyB,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;AAC/D,CAAC;AAED,SAAgB,aAAa,CAAC,IAAY;IACxC,qEAAqE;IACrE,uBAAuB;IACvB,MAAM,MAAM,GAAG,GAAG,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC;IAC/C,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;QAChC,OAAO,MAAM,CAAC;IAChB,CAAC;IACD,OAAO,MAAM,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;AAC3C,CAAC;AAED,+DAA+D;AAC/D,SAAgB,yBAAyB,CAAC,IAAY;IACpD,OAAO,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,EAAE,CAAC,CAAC;AACjD,CAAC;AAED,0CAA0C;AAC1C,SAAgB,0BAA0B,CAAC,IAAY;IACrD,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;AACzC,CAAC;AAED,mEAAmE;AACnE,SAAgB,oBAAoB,CAAC,QAAgB;IACnD,OAAO,QAAQ,CAAC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;AAChD,CAAC;AAED,SAAgB,0BAA0B,CAAC,IAAY;IACrD,OAAO,IAAI;SACR,KAAK,CAAC,GAAG,CAAC;SACV,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE;QACjB,IAAI,cAAc,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;YAC9B,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC,EAAE,EAAc,CAAC;SACjB,IAAI,CAAC,GAAG,CAAC,CAAC;AACf,CAAC;AAED,SAAgB,8BAA8B,CAAC,IAAY;IACzD,OAAO,0BAA0B,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;AACnE,CAAC;AAED;;;;GAIG;AACH,SAAgB,YAAY,CAAC,IAAY;IACvC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,oCAAoC,CAAC,KAAK,IAAI,CAAC;AAC5F,CAAC", "sourcesContent": ["/** Match `[page]` -> `page` or `[...group]` -> `...group` */\nconst dynamicNameRe = /^\\[([^[\\]]+?)\\]$/;\n\ninterface DynamicNameMatch {\n  name: string;\n  deep: boolean;\n}\n\n/** Match `[page]` -> `page` */\nexport function matchDynamicName(name: string): DynamicNameMatch | undefined {\n  const paramName = name.match(dynamicNameRe)?.[1];\n  if (paramName == null) {\n    return undefined;\n  } else if (paramName.startsWith('...')) {\n    return { name: paramName.slice(3), deep: true };\n  } else {\n    return { name: paramName, deep: false };\n  }\n}\n\n/** Test `/` -> `page` */\nexport function testNotFound(name: string): boolean {\n  return /\\+not-found$/.test(name);\n}\n\n/** Match `(page)` -> `page` */\nexport function matchGroupName(name: string): string | undefined {\n  return name.match(/^(?:[^\\\\()])*?\\(([^\\\\/]+)\\)/)?.[1];\n}\n\n/** Match `(app)/(page)` -> `page` */\nexport function matchLastGroupName(name: string): string | undefined {\n  return name.match(/.*(?:\\/|^)\\(([^\\\\/]+)\\)[^\\s]*$/)?.[1];\n}\n\n/** Match the first array group name `(a,b,c)/(d,c)` -> `'a,b,c'` */\nexport function matchArrayGroupName(name: string) {\n  return name.match(/(?:[^\\\\()])*?\\(([^\\\\/]+,[^\\\\/]+)\\)/)?.[1];\n}\n\nexport function getNameFromFilePath(name: string): string {\n  return removeSupportedExtensions(removeFileSystemDots(name));\n}\n\nexport function getContextKey(name: string): string {\n  // The root path is `` (empty string) so always prepend `/` to ensure\n  // there is some value.\n  const normal = '/' + getNameFromFilePath(name);\n  if (!normal.endsWith('_layout')) {\n    return normal;\n  }\n  return normal.replace(/\\/?_layout$/, '');\n}\n\n/** Remove `.js`, `.ts`, `.jsx`, `.tsx`, and the +api suffix */\nexport function removeSupportedExtensions(name: string): string {\n  return name.replace(/(\\+api)?\\.[jt]sx?$/g, '');\n}\n\n/** Remove `.js`, `.ts`, `.jsx`, `.tsx` */\nexport function removeFileSystemExtensions(name: string): string {\n  return name.replace(/\\.[jt]sx?$/g, '');\n}\n\n// Remove any amount of `./` and `../` from the start of the string\nexport function removeFileSystemDots(filePath: string): string {\n  return filePath.replace(/^(?:\\.\\.?\\/)+/g, '');\n}\n\nexport function stripGroupSegmentsFromPath(path: string): string {\n  return path\n    .split('/')\n    .reduce((acc, v) => {\n      if (matchGroupName(v) == null) {\n        acc.push(v);\n      }\n      return acc;\n    }, [] as string[])\n    .join('/');\n}\n\nexport function stripInvisibleSegmentsFromPath(path: string): string {\n  return stripGroupSegmentsFromPath(path).replace(/\\/?index$/, '');\n}\n\n/**\n * Match:\n *  - _layout files, +html, +not-found, string+api, etc\n *  - Routes can still use `+`, but it cannot be in the last segment.\n */\nexport function isTypedRoute(name: string) {\n  return !name.startsWith('+') && name.match(/(_layout|[^/]*?\\+[^/]*?)\\.[tj]sx?$/) === null;\n}\n"]}