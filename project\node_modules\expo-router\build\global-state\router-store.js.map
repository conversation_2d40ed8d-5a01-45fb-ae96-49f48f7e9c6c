{"version": 3, "file": "router-store.js", "sourceRoot": "", "sources": ["../../src/global-state/router-store.tsx"], "names": [], "mappings": ";AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgIb,4BAwFC;AAUD,oCAEC;AAlOD,qDAMkC;AAClC,oEAAuC;AACvC,iCAAiF;AACjF,+CAAwC;AAGxC,mEAAoE;AACpE,2EAAqE;AACrE,0DAAiG;AACjG,0EAAiE;AACjE,4CAAyC;AAEzC,2CAAiF;AAEjF,8CAA2D;AAC3D,sCAAoD;AACpD,8DAAgD;AAmBhD,MAAM,QAAQ,GAAG;IACf,OAAO,EAAE,EAAc;CACxB,CAAC;AAEF,MAAM,cAAc,GAAG,IAAI,OAAO,EAAuD,CAAC;AAE1F,IAAI,0BAA8C,CAAC;AACnD,IAAI,wBAAwB,GAAG,KAAK,CAAC;AAExB,QAAA,KAAK,GAAG;IACnB,kBAAkB;QAChB,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC;IAC/E,CAAC;IACD,IAAI,KAAK;QACP,OAAO,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC;IAChC,CAAC;IACD,IAAI,aAAa;QACf,OAAO,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC;IACxC,CAAC;IACD,IAAI,SAAS;QACX,OAAO,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC;IACpC,CAAC;IACD,YAAY;QACV,OAAO,QAAQ,CAAC,OAAO,CAAC,SAAS,IAAI,4BAAgB,CAAC;IACxD,CAAC;IACD,IAAI,SAAS;QACX,OAAO,QAAQ,CAAC,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC;IAC1C,CAAC;IACD,IAAI,aAAa;QACf,OAAO,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC;IACxC,CAAC;IACD,IAAI,OAAO;QACT,OAAO,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC;IAClC,CAAC;IACD,eAAe,CAAC,KAAwB;QACtC,MAAM,SAAS,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAC5C,QAAQ,CAAC,OAAO,CAAC,SAAS,GAAG,SAAS,CAAC;IACzC,CAAC;IACD,OAAO;QACL,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAC9B,wBAAwB,GAAG,IAAI,CAAC;YAChC,iGAAiG;YACjG,0BAA0B,GAAG,qBAAqB,CAAC,GAAG,EAAE;gBACtD,YAAY,CAAC,wBAAwB,EAAE,EAAE,CAAC;YAC5C,CAAC,CAAC,CAAC;QACL,CAAC;QAED,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YACxD,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBAClB,OAAO;YACT,CAAC;YAED,IAAI,OAAO,GAAwB,KAAK,CAAC;YACzC,IAAI,KAAK,GAAqC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;YAE3D,OAAO,CAAC,OAAO,IAAI,KAAK,EAAE,CAAC;gBACzB,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC;gBACtB,KAAK;oBACH,KAAK,CAAC,MAAM,EAAE,CACZ,OAAO,IAAI,KAAK,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,QAAQ;wBACjD,CAAC,CAAC,KAAK,CAAC,KAAK;wBACb,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAC5B,EAAE,KAAK,CAAC;YACb,CAAC;YAED,QAAQ,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;YAEtC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,QAAQ,CAAC,OAAO,CAAC,SAAS,GAAG,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChE,CAAC;YAED,KAAK,MAAM,QAAQ,IAAI,oBAAoB,EAAE,CAAC;gBAC5C,QAAQ,EAAE,CAAC;YACb,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IACD,aAAa;QACX,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC;YAC9C,MAAM,IAAI,KAAK,CACb,gKAAgK,CACjK,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAC;AAEF,SAAgB,QAAQ,CACtB,OAAuB,EACvB,oBAA0C,EAC1C,SAAkB;IAElB,MAAM,aAAa,GAAG,IAAA,kCAAyB,GAAE,CAAC;IAClD,MAAM,MAAM,GAAG,wBAAS,CAAC,UAAU,EAAE,KAAK,EAAE,MAAM,CAAC;IAEnD,IAAI,OAAuC,CAAC;IAC5C,IAAI,aAAa,GAAuB,gBAAQ,CAAC;IACjD,IAAI,YAA8C,CAAC;IAEnD,MAAM,SAAS,GAAG,IAAA,qBAAS,EAAC,OAAO,EAAE;QACnC,GAAG,MAAM;QACT,iBAAiB,EAAE,IAAI;QACvB,QAAQ,EAAE,uBAAQ,CAAC,EAAE;KACtB,CAAC,CAAC;IAEH,MAAM,SAAS,GAAqB,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,CAAC;SACtE,MAAM,CAAC,OAAO,CAAC;SACf,IAAI,EAAE;SACN,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;QACb,OAAO;YACL,IAAA,4CAAmB,EAAC,IAAA,6CAAkB,EAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YACrD,KAAK;YACL,IAAA,0BAAoB,EAAC,KAAK,CAAC,WAAW,CAAC;SACxC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEL,IAAI,SAAS,EAAE,CAAC;QACd,mEAAmE;QACnE,OAAO,GAAG,IAAA,mCAAgB,EAAC,SAAS,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,aAAK,CAAC,YAAY,EAAE,EAAE;YACzE,QAAQ,EAAE,oBAAoB,CAAC,QAAQ;YACvC,SAAS;YACT,SAAS;SACV,CAAC,CAAC;QACH,aAAa,GAAG,IAAA,uCAA0B,EAAC,SAAS,CAAC,CAAC;QAEtD,sHAAsH;QACtH,+EAA+E;QAC/E,sHAAsH;QACtH,MAAM,UAAU,GAAG,OAAO,EAAE,aAAa,EAAE,EAAE,CAAC;QAC9C,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;YACnC,IAAI,WAAW,GAAG,IAAA,2CAAsB,EAAC,OAAO,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAEvE,kGAAkG;YAClG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC;gBAAE,WAAW,GAAG,GAAG,GAAG,WAAW,CAAC;YAElE,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;YACrE,MAAM,gBAAgB,GAAG,IAAA,iCAAqB,EAAC,YAAY,CAAC,CAAC;YAC7D,cAAc,CAAC,GAAG,CAAC,YAAmB,EAAE,gBAAgB,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;SAAM,CAAC;QACN,8EAA8E;QAC9E,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;YAC1C,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;QACrC,CAAC;QAED,qDAAqD;QACrD,aAAa,GAAG,gBAAQ,CAAC;IAC3B,CAAC;IAED,QAAQ,CAAC,OAAO,GAAG;QACjB,aAAa;QACb,SAAS;QACT,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;QACT,KAAK,EAAE,YAAY;KACpB,CAAC;IAEF,IAAI,YAAY,EAAE,CAAC;QACjB,QAAQ,CAAC,OAAO,CAAC,SAAS,GAAG,kBAAkB,CAAC,YAAY,CAAC,CAAC;IAChE,CAAC;IAED,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,OAAO,GAAG,EAAE;YACV,cAAc;YAEd,IAAI,0BAA0B,EAAE,CAAC;gBAC/B,oBAAoB,CAAC,0BAA0B,CAAC,CAAC;gBACjD,0BAA0B,GAAG,SAAS,CAAC;YACzC,CAAC;QACH,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,OAAO,aAAK,CAAC;AACf,CAAC;AAED,MAAM,oBAAoB,GAAG,IAAI,GAAG,EAAc,CAAC;AACnD,MAAM,kBAAkB,GAAG,CAAC,QAAoB,EAAE,EAAE;IAClD,oBAAoB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACnC,OAAO,GAAG,EAAE;QACV,oBAAoB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,SAAgB,YAAY;IAC1B,OAAO,IAAA,4BAAoB,EAAC,kBAAkB,EAAE,aAAK,CAAC,YAAY,EAAE,aAAK,CAAC,YAAY,CAAC,CAAC;AAC1F,CAAC;AAED,SAAS,kBAAkB,CAAC,KAA2B;IACrD,IAAI,SAAS,GAAG,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAE1C,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,SAAS,GAAG,IAAA,iCAAqB,EAAC,KAAK,CAAC,CAAC;QAEzC,MAAM,iBAAiB,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC;QACrD,IAAI,iBAAiB,EAAE,CAAC;YACtB,MAAM,QAAQ,GACZ,SAAS,CAAC,QAAQ,CAAC,MAAM,KAAK,iBAAiB,CAAC,QAAQ,CAAC,MAAM;gBAC/D,SAAS,CAAC,QAAQ,CAAC,KAAK,CACtB,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,iBAAiB,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,OAAO,CAClE;gBACD,SAAS,CAAC,kBAAkB,KAAK,iBAAiB,CAAC,kBAAkB,CAAC;YAExE,IAAI,QAAQ,EAAE,CAAC;gBACb,gFAAgF;gBAChF,SAAS,GAAG,iBAAiB,CAAC;YAChC,CAAC;QACH,CAAC;QAED,cAAc,CAAC,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;IACvC,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC", "sourcesContent": ["'use client';\n\nimport {\n  NavigationContainerRefWithCurrent,\n  NavigationState,\n  PartialState,\n  useNavigationContainerRef,\n  useStateForPath,\n} from '@react-navigation/native';\nimport Constants from 'expo-constants';\nimport { ComponentType, Fragment, useEffect, useSyncExternalStore } from 'react';\nimport { Platform } from 'react-native';\n\nimport { RouteNode } from '../Route';\nimport { extractExpoPathFromURL } from '../fork/extractPathFromURL';\nimport { routePatternToRegex } from '../fork/getStateFromPath-forks';\nimport { ExpoLinkingOptions, LinkingConfigOptions, getLinkingConfig } from '../getLinkingConfig';\nimport { parseRouteSegments } from '../getReactNavigationConfig';\nimport { getRoutes } from '../getRoutes';\nimport { RedirectConfig } from '../getRoutesCore';\nimport { defaultRouteInfo, getRouteInfoFromState, UrlObject } from './routeInfo';\nimport { RequireContext } from '../types';\nimport { getQualifiedRouteComponent } from '../useScreens';\nimport { shouldLinkExternally } from '../utils/url';\nimport * as SplashScreen from '../views/Splash';\n\nexport type StoreRedirects = readonly [RegExp, RedirectConfig, boolean];\nexport type ReactNavigationState = NavigationState | PartialState<NavigationState>;\nexport type FocusedRouteState = NonNullable<ReturnType<typeof useStateForPath>>;\n\nexport type RouterStore = typeof store;\n\ntype StoreRef = {\n  navigationRef: NavigationContainerRefWithCurrent<ReactNavigation.RootParamList>;\n  routeNode: RouteNode | null;\n  rootComponent: ComponentType<any>;\n  state?: ReactNavigationState;\n  linking?: ExpoLinkingOptions;\n  config: any;\n  redirects: StoreRedirects[];\n  routeInfo?: UrlObject;\n};\n\nconst storeRef = {\n  current: {} as StoreRef,\n};\n\nconst routeInfoCache = new WeakMap<FocusedRouteState | ReactNavigationState, UrlObject>();\n\nlet splashScreenAnimationFrame: number | undefined;\nlet hasAttemptedToHideSplash = false;\n\nexport const store = {\n  shouldShowTutorial() {\n    return !storeRef.current.routeNode && process.env.NODE_ENV === 'development';\n  },\n  get state() {\n    return storeRef.current.state;\n  },\n  get navigationRef() {\n    return storeRef.current.navigationRef;\n  },\n  get routeNode() {\n    return storeRef.current.routeNode;\n  },\n  getRouteInfo(): UrlObject {\n    return storeRef.current.routeInfo || defaultRouteInfo;\n  },\n  get redirects() {\n    return storeRef.current.redirects || [];\n  },\n  get rootComponent() {\n    return storeRef.current.rootComponent;\n  },\n  get linking() {\n    return storeRef.current.linking;\n  },\n  setFocusedState(state: FocusedRouteState) {\n    const routeInfo = getCachedRouteInfo(state);\n    storeRef.current.routeInfo = routeInfo;\n  },\n  onReady() {\n    if (!hasAttemptedToHideSplash) {\n      hasAttemptedToHideSplash = true;\n      // NOTE(EvanBacon): `navigationRef.isReady` is sometimes not true when state is called initially.\n      splashScreenAnimationFrame = requestAnimationFrame(() => {\n        SplashScreen._internal_maybeHideAsync?.();\n      });\n    }\n\n    storeRef.current.navigationRef.addListener('state', (e) => {\n      if (!e.data.state) {\n        return;\n      }\n\n      let isStale: boolean | undefined = false;\n      let state: ReactNavigationState | undefined = e.data.state;\n\n      while (!isStale && state) {\n        isStale = state.stale;\n        state =\n          state.routes?.[\n            'index' in state && typeof state.index === 'number'\n              ? state.index\n              : state.routes.length - 1\n          ]?.state;\n      }\n\n      storeRef.current.state = e.data.state;\n\n      if (!isStale) {\n        storeRef.current.routeInfo = getCachedRouteInfo(e.data.state);\n      }\n\n      for (const callback of routeInfoSubscribers) {\n        callback();\n      }\n    });\n  },\n  assertIsReady() {\n    if (!storeRef.current.navigationRef.isReady()) {\n      throw new Error(\n        'Attempted to navigate before mounting the Root Layout component. Ensure the Root Layout component is rendering a Slot, or other navigator on the first render.'\n      );\n    }\n  },\n};\n\nexport function useStore(\n  context: RequireContext,\n  linkingConfigOptions: LinkingConfigOptions,\n  serverUrl?: string\n) {\n  const navigationRef = useNavigationContainerRef();\n  const config = Constants.expoConfig?.extra?.router;\n\n  let linking: ExpoLinkingOptions | undefined;\n  let rootComponent: ComponentType<any> = Fragment;\n  let initialState: ReactNavigationState | undefined;\n\n  const routeNode = getRoutes(context, {\n    ...config,\n    ignoreEntryPoints: true,\n    platform: Platform.OS,\n  });\n\n  const redirects: StoreRedirects[] = [config?.redirects, config?.rewrites]\n    .filter(Boolean)\n    .flat()\n    .map((route) => {\n      return [\n        routePatternToRegex(parseRouteSegments(route.source)),\n        route,\n        shouldLinkExternally(route.destination),\n      ];\n    });\n\n  if (routeNode) {\n    // We have routes, so get the linking config and the root component\n    linking = getLinkingConfig(routeNode, context, () => store.getRouteInfo(), {\n      metaOnly: linkingConfigOptions.metaOnly,\n      serverUrl,\n      redirects,\n    });\n    rootComponent = getQualifiedRouteComponent(routeNode);\n\n    // By default React Navigation is async and does not render anything in the first pass as it waits for `getInitialURL`\n    // This will cause static rendering to fail, which once performs a single pass.\n    // If the initialURL is a string, we can prefetch the state and routeInfo, skipping React Navigation's async behavior.\n    const initialURL = linking?.getInitialURL?.();\n    if (typeof initialURL === 'string') {\n      let initialPath = extractExpoPathFromURL(linking.prefixes, initialURL);\n\n      // It does not matter if the path starts with a `/` or not, but this keeps the behavior consistent\n      if (!initialPath.startsWith('/')) initialPath = '/' + initialPath;\n\n      initialState = linking.getStateFromPath(initialPath, linking.config);\n      const initialRouteInfo = getRouteInfoFromState(initialState);\n      routeInfoCache.set(initialState as any, initialRouteInfo);\n    }\n  } else {\n    // Only error in production, in development we will show the onboarding screen\n    if (process.env.NODE_ENV === 'production') {\n      throw new Error('No routes found');\n    }\n\n    // In development, we will show the onboarding screen\n    rootComponent = Fragment;\n  }\n\n  storeRef.current = {\n    navigationRef,\n    routeNode,\n    config,\n    rootComponent,\n    linking,\n    redirects,\n    state: initialState,\n  };\n\n  if (initialState) {\n    storeRef.current.routeInfo = getCachedRouteInfo(initialState);\n  }\n\n  useEffect(() => {\n    return () => {\n      // listener();\n\n      if (splashScreenAnimationFrame) {\n        cancelAnimationFrame(splashScreenAnimationFrame);\n        splashScreenAnimationFrame = undefined;\n      }\n    };\n  });\n\n  return store;\n}\n\nconst routeInfoSubscribers = new Set<() => void>();\nconst routeInfoSubscribe = (callback: () => void) => {\n  routeInfoSubscribers.add(callback);\n  return () => {\n    routeInfoSubscribers.delete(callback);\n  };\n};\n\nexport function useRouteInfo() {\n  return useSyncExternalStore(routeInfoSubscribe, store.getRouteInfo, store.getRouteInfo);\n}\n\nfunction getCachedRouteInfo(state: ReactNavigationState) {\n  let routeInfo = routeInfoCache.get(state);\n\n  if (!routeInfo) {\n    routeInfo = getRouteInfoFromState(state);\n\n    const previousRouteInfo = storeRef.current.routeInfo;\n    if (previousRouteInfo) {\n      const areEqual =\n        routeInfo.segments.length === previousRouteInfo.segments.length &&\n        routeInfo.segments.every(\n          (segment, index) => previousRouteInfo.segments[index] === segment\n        ) &&\n        routeInfo.pathnameWithParams === previousRouteInfo.pathnameWithParams;\n\n      if (areEqual) {\n        // If they are equal, keep the previous route info for object reference equality\n        routeInfo = previousRouteInfo;\n      }\n    }\n\n    routeInfoCache.set(state, routeInfo);\n  }\n\n  return routeInfo;\n}\n"]}