{"version": 3, "file": "Sitemap.js", "sourceRoot": "", "sources": ["../../src/views/Sitemap.tsx"], "names": [], "mappings": ";AAAA,mCAAmC;AACnC,YAAY,CAAC;;;;;AAuBb,sCAiCC;AAED,0BAkBC;AAzED,kDAA0B;AAC1B,+CASsB;AACtB,mFAA8D;AAE9D,2CAAwD;AACxD,6CAAuD;AACvD,uCAAoC;AACpC,kDAAkE;AAElE,MAAM,MAAM,GAAG,EAAE,CAAC;AAElB,SAAgB,aAAa;IAC3B,OAAO;QACL,KAAK,EAAE,SAAS;QAChB,YAAY,EAAE,OAAO;QACrB,gBAAgB,EAAE,KAAK;QACvB,gBAAgB,EAAE;YAChB,KAAK,EAAE,OAAO;SACf;QACD,eAAe,EAAE,OAAO;QACxB,qBAAqB,EAAE;YACrB,KAAK,EAAE,OAAO;SACf;QACD,WAAW,EAAE;YACX,eAAe,EAAE,OAAO;YACxB,6BAA6B;YAC7B,iBAAiB,EAAE,SAAS;SAC7B;QACD,MAAM,EAAE,GAAG,EAAE;YACX,MAAM,cAAc,GAAG,uBAAQ,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,6CAAY,CAAC,CAAC,CAAC,mBAAI,CAAC;YACvE,OAAO,CACL,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CACnC;UAAA,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAChC;YAAA,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAC7B;cAAA,CAAC,WAAW,CAAC,AAAD,EACd;YAAA,EAAE,mBAAI,CACN;YAAA,CAAC,mBAAI,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CACtD;;YACF,EAAE,mBAAI,CACR;UAAA,EAAE,mBAAI,CACR;QAAA,EAAE,cAAc,CAAC,CAClB,CAAC;QACJ,CAAC;KACF,CAAC;AACJ,CAAC;AAED,SAAgB,OAAO;IACrB,MAAM,OAAO,GAAG,IAAA,uBAAU,GAAE,CAAC;IAC7B,MAAM,QAAQ,GAAG,eAAK,CAAC,OAAO,CAC5B,GAAG,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,CAAC,UAAU,CAAC,IAAI,EAAE,EACrE,CAAC,OAAO,CAAC,CACV,CAAC;IACF,OAAO,CACL,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAC5B;MAAA,CAAC,wCAA4B,IAAI,CAAC,wBAAS,CAAC,QAAQ,CAAC,eAAe,EAAG,CACvE;MAAA,CAAC,yBAAU,CAAC,qBAAqB,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAC/C;QAAA,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CACvB,CAAC,mBAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CACvF;YAAA,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,EAC3B;UAAA,EAAE,mBAAI,CAAC,CACR,CAAC,CACJ;MAAA,EAAE,yBAAU,CACd;IAAA,EAAE,mBAAI,CAAC,CACR,CAAC;AACJ,CAAC;AAQD,SAAS,WAAW,CAAC,EAAE,IAAI,EAAE,KAAK,GAAG,CAAC,EAAoB;IACxD,MAAM,QAAQ,GAAG,eAAK,CAAC,OAAO,CAC5B,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,mBAAmB,CAAC,EAC5E,CAAC,IAAI,CAAC,CACP,CAAC;IACF,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC;IAE9E,IAAI,QAAQ,EAAE,CAAC;QACb,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC;IACrE,CAAC;IACD,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC;AACvE,CAAC;AACD,SAAS,iBAAiB,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAA8B;IAC1E,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,GAAG,eAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAE3D,OAAO,CACL,EACE;MAAA,CAAC,oBAAoB,CACnB,KAAK,CAAC,CAAC,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,CACxB,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,AAAD,EAAG,CAAC,CACtB,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAG,CAAC,CAC1D,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CACxB,KAAK,CAAC,CAAC,KAAK,CAAC,CACb,IAAI,CAAC,CAAC,IAAI,CAAC,CACX,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,cAAc,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAEjD;MAAA,CAAC,CAAC,WAAW;YACX,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAC3B,CAAC,WAAW,CACV,GAAG,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CACtB,IAAI,CAAC,CAAC,KAAK,CAAC,CACZ,KAAK,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAC1C,CACH,CAAC,CACN;IAAA,GAAG,CACJ,CAAC;AACJ,CAAC;AAED,SAAS,mBAAmB,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAA8B;IAC5E,OAAO,CACL,CAAC,WAAI,CACH,kBAAkB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CACpC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAChB,OAAO;IACP,mEAAmE;IACnE,OAAO,CACP;MAAA,CAAC,oBAAoB,CACnB,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,AAAD,EAAG,CAAC,CACvB,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,AAAD,EAAG,CAAC,CAC3B,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CACxB,KAAK,CAAC,CAAC,KAAK,CAAC,CACb,IAAI,CAAC,CAAC,IAAI,CAAC,EAEf;IAAA,EAAE,WAAI,CAAC,CACR,CAAC;AACJ,CAAC;AAED,SAAS,oBAAoB,CAAC,EAC5B,KAAK,EACL,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,KAAK,EACL,IAAI,EACJ,GAAG,cAAc,EAQ2B;IAC5C,OAAO,CACL,CAAC,qBAAS,CAAC,IAAI,cAAc,CAAC,CAC5B;MAAA,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CACzB,CAAC,mBAAI,CACH,MAAM,CAAC,cAAc,CACrB,KAAK,CAAC,CAAC;gBACL,MAAM,CAAC,aAAa;gBACpB;oBACE,WAAW,EAAE,MAAM,GAAG,KAAK,GAAG,MAAM;oBACpC,eAAe,EAAE,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa;iBACrD;gBACD,OAAO,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE;gBACzC,KAAK;aACN,CAAC,CACF;UAAA,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,CAC1D;YAAA,CAAC,QAAQ,CACT;YAAA,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,mBAAI,CAChD;UAAA,EAAE,mBAAI,CAEN;;UAAA,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,CAC1D;YAAA,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,mBAAI,CAAC,CAC3E;YAAA,CAAC,SAAS,CACZ;UAAA,EAAE,mBAAI,CACR;QAAA,EAAE,mBAAI,CAAC,CACR,CACH;IAAA,EAAE,qBAAS,CAAC,CACb,CAAC;AACJ,CAAC;AAED,SAAS,QAAQ;IACf,OAAO,CAAC,oBAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC,EAAG,CAAC;AACxF,CAAC;AAED,SAAS,OAAO;IACd,OAAO,CAAC,oBAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAC,EAAG,CAAC;AACvF,CAAC;AAED,SAAS,WAAW;IAClB,OAAO,CAAC,oBAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC,EAAG,CAAC;AAC3F,CAAC;AAED,SAAS,WAAW;IAClB,OAAO,CAAC,oBAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC,EAAG,CAAC;AAC3F,CAAC;AAED,SAAS,SAAS,CAAC,EAAE,QAAQ,GAAG,CAAC,EAAyB;IACxD,OAAO,CACL,CAAC,oBAAK,CACJ,KAAK,CAAC,CAAC;YACL,MAAM,CAAC,KAAK;YACZ;gBACE,SAAS,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,QAAQ,KAAK,EAAE,CAAC;aAC1C;SACF,CAAC,CACF,MAAM,CAAC,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAC,EACrD,CACH,CAAC;AACJ,CAAC;AAED,MAAM,MAAM,GAAG,yBAAU,CAAC,MAAM,CAAC;IAC/B,SAAS,EAAE;QACT,eAAe,EAAE,OAAO;QACxB,IAAI,EAAE,CAAC;QACP,UAAU,EAAE,SAAS;KACtB;IACD,MAAM,EAAE;QACN,eAAe,EAAE,SAAS;QAC1B,eAAe,EAAE,EAAE;QACnB,iBAAiB,EAAE,CAAC;QACpB,WAAW,EAAE,SAAS;QACtB,WAAW,EAAE,MAAM;QACnB,YAAY,EAAE;YACZ,KAAK,EAAE,CAAC;YACR,MAAM,EAAE,CAAC;SACV;QACD,aAAa,EAAE,IAAI;QACnB,YAAY,EAAE,CAAC;QACf,SAAS,EAAE,CAAC;KACb;IACD,aAAa,EAAE;QACb,aAAa,EAAE,KAAK;QACpB,UAAU,EAAE,QAAQ;QACpB,GAAG,EAAE,EAAE;QACP,iBAAiB,EAAE,IAAI;QACvB,GAAG,uBAAQ,CAAC,MAAM,CAAC;YACjB,GAAG,EAAE;gBACH,KAAK,EAAE,MAAM;gBACb,QAAQ,EAAE,GAAG;gBACb,gBAAgB,EAAE,MAAM;aACzB;SACF,CAAC;KACH;IACD,KAAK,EAAE;QACL,KAAK,EAAE,OAAO;QACd,QAAQ,EAAE,EAAE;QACZ,UAAU,EAAE,MAAM;KACnB;IACD,MAAM,EAAE;QACN,iBAAiB,EAAE,IAAI;QACvB,eAAe,EAAE,EAAE;QACnB,GAAG,uBAAQ,CAAC,MAAM,CAAC;YACjB,GAAG,EAAE;gBACH,aAAa,EAAE,EAAE;aAClB;YACD,GAAG,EAAE;gBACH,KAAK,EAAE,MAAM;gBACb,QAAQ,EAAE,GAAG;gBACb,gBAAgB,EAAE,MAAM;gBACxB,aAAa,EAAE,EAAE;aAClB;YACD,OAAO,EAAE;gBACP,aAAa,EAAE,EAAE;aAClB;SACF,CAAC;KACH;IACD,aAAa,EAAE;QACb,WAAW,EAAE,CAAC;QACd,WAAW,EAAE,SAAS;QACtB,eAAe,EAAE,SAAS;QAC1B,YAAY,EAAE,EAAE;QAChB,YAAY,EAAE,EAAE;QAChB,QAAQ,EAAE,QAAQ;KACnB;IACD,aAAa,EAAE;QACb,iBAAiB,EAAE,MAAM;QACzB,eAAe,EAAE,EAAE;QACnB,aAAa,EAAE,KAAK;QACpB,cAAc,EAAE,eAAe;QAC/B,UAAU,EAAE,QAAQ;QACpB,GAAG,uBAAQ,CAAC,MAAM,CAAC;YACjB,GAAG,EAAE;gBACH,kBAAkB,EAAE,OAAO;aAC5B;SACF,CAAC;KACH;IACD,QAAQ,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE;IAC1D,OAAO,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE;IAC/C,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,EAAE;IACrE,UAAU,EAAE;QACV,KAAK,EAAE,EAAE;QACT,MAAM,EAAE,EAAE;QACV,eAAe,EAAE,SAAS;QAC1B,YAAY,EAAE,CAAC;QACf,aAAa,EAAE,KAAK;QACpB,UAAU,EAAE,QAAQ;QACpB,cAAc,EAAE,QAAQ;KACzB;CACF,CAAC,CAAC", "sourcesContent": ["// Copyright © 2024 650 Industries.\n'use client';\n\nimport { NativeStackNavigationOptions } from '@react-navigation/native-stack';\nimport React from 'react';\nimport {\n  Image,\n  StyleSheet,\n  Text,\n  View,\n  ScrollView,\n  Platform,\n  StatusBar,\n  ViewStyle,\n} from 'react-native';\nimport { SafeAreaView } from 'react-native-safe-area-context';\n\nimport { Pressable, PressableProps } from './Pressable';\nimport { useSitemap, SitemapType } from './useSitemap';\nimport { Link } from '../link/Link';\nimport { canOverrideStatusBarBehavior } from '../utils/statusbar';\n\nconst INDENT = 20;\n\nexport function getNavOptions(): NativeStackNavigationOptions {\n  return {\n    title: 'sitemap',\n    presentation: 'modal',\n    headerLargeTitle: false,\n    headerTitleStyle: {\n      color: 'white',\n    },\n    headerTintColor: 'white',\n    headerLargeTitleStyle: {\n      color: 'white',\n    },\n    headerStyle: {\n      backgroundColor: 'black',\n      // @ts-expect-error: mistyped\n      borderBottomColor: '#323232',\n    },\n    header: () => {\n      const WrapperElement = Platform.OS === 'android' ? SafeAreaView : View;\n      return (\n        <WrapperElement style={styles.header}>\n          <View style={styles.headerContent}>\n            <View style={styles.headerIcon}>\n              <SitemapIcon />\n            </View>\n            <Text role=\"heading\" aria-level={1} style={styles.title}>\n              Sitemap\n            </Text>\n          </View>\n        </WrapperElement>\n      );\n    },\n  };\n}\n\nexport function Sitemap() {\n  const sitemap = useSitemap();\n  const children = React.useMemo(\n    () => sitemap?.children.filter(({ isInternal }) => !isInternal) ?? [],\n    [sitemap]\n  );\n  return (\n    <View style={styles.container}>\n      {canOverrideStatusBarBehavior && <StatusBar barStyle=\"light-content\" />}\n      <ScrollView contentContainerStyle={styles.scroll}>\n        {children.map((child) => (\n          <View testID=\"sitemap-item-container\" key={child.contextKey} style={styles.itemContainer}>\n            <SitemapItem node={child} />\n          </View>\n        ))}\n      </ScrollView>\n    </View>\n  );\n}\n\ninterface SitemapItemProps {\n  node: SitemapType;\n  level?: number;\n  info?: string;\n}\n\nfunction SitemapItem({ node, level = 0 }: SitemapItemProps) {\n  const isLayout = React.useMemo(\n    () => node.children.length > 0 || node.contextKey.match(/_layout\\.[jt]sx?$/),\n    [node]\n  );\n  const info = node.isInitial ? 'Initial' : node.isGenerated ? 'Generated' : '';\n\n  if (isLayout) {\n    return <LayoutSitemapItem node={node} level={level} info={info} />;\n  }\n  return <StandardSitemapItem node={node} level={level} info={info} />;\n}\nfunction LayoutSitemapItem({ node, level, info }: Required<SitemapItemProps>) {\n  const [isCollapsed, setIsCollapsed] = React.useState(true);\n\n  return (\n    <>\n      <SitemapItemPressable\n        style={{ opacity: 0.4 }}\n        leftIcon={<PkgIcon />}\n        rightIcon={<ArrowIcon rotation={isCollapsed ? 0 : 180} />}\n        filename={node.filename}\n        level={level}\n        info={info}\n        onPress={() => setIsCollapsed((prev) => !prev)}\n      />\n      {!isCollapsed &&\n        node.children.map((child) => (\n          <SitemapItem\n            key={child.contextKey}\n            node={child}\n            level={level + (node.isGenerated ? 0 : 1)}\n          />\n        ))}\n    </>\n  );\n}\n\nfunction StandardSitemapItem({ node, info, level }: Required<SitemapItemProps>) {\n  return (\n    <Link\n      accessibilityLabel={node.contextKey}\n      href={node.href}\n      asChild\n      // Ensure we replace the history so you can't go back to this page.\n      replace>\n      <SitemapItemPressable\n        leftIcon={<FileIcon />}\n        rightIcon={<ForwardIcon />}\n        filename={node.filename}\n        level={level}\n        info={info}\n      />\n    </Link>\n  );\n}\n\nfunction SitemapItemPressable({\n  style,\n  leftIcon,\n  rightIcon,\n  filename,\n  level,\n  info,\n  ...pressableProps\n}: {\n  style?: ViewStyle;\n  leftIcon?: React.ReactNode;\n  rightIcon?: React.ReactNode;\n  filename: string;\n  level: number;\n  info?: string;\n} & Omit<PressableProps, 'style' | 'children'>) {\n  return (\n    <Pressable {...pressableProps}>\n      {({ pressed, hovered }) => (\n        <View\n          testID=\"sitemap-item\"\n          style={[\n            styles.itemPressable,\n            {\n              paddingLeft: INDENT + level * INDENT,\n              backgroundColor: hovered ? '#202425' : 'transparent',\n            },\n            pressed && { backgroundColor: '#26292b' },\n            style,\n          ]}>\n          <View style={{ flexDirection: 'row', alignItems: 'center' }}>\n            {leftIcon}\n            <Text style={styles.filename}>{filename}</Text>\n          </View>\n\n          <View style={{ flexDirection: 'row', alignItems: 'center' }}>\n            {!!info && <Text style={[styles.virtual, { marginRight: 8 }]}>{info}</Text>}\n            {rightIcon}\n          </View>\n        </View>\n      )}\n    </Pressable>\n  );\n}\n\nfunction FileIcon() {\n  return <Image style={styles.image} source={require('expo-router/assets/file.png')} />;\n}\n\nfunction PkgIcon() {\n  return <Image style={styles.image} source={require('expo-router/assets/pkg.png')} />;\n}\n\nfunction ForwardIcon() {\n  return <Image style={styles.image} source={require('expo-router/assets/forward.png')} />;\n}\n\nfunction SitemapIcon() {\n  return <Image style={styles.image} source={require('expo-router/assets/sitemap.png')} />;\n}\n\nfunction ArrowIcon({ rotation = 0 }: { rotation?: number }) {\n  return (\n    <Image\n      style={[\n        styles.image,\n        {\n          transform: [{ rotate: `${rotation}deg` }],\n        },\n      ]}\n      source={require('expo-router/assets/arrow_down.png')}\n    />\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    backgroundColor: 'black',\n    flex: 1,\n    alignItems: 'stretch',\n  },\n  header: {\n    backgroundColor: '#151718',\n    paddingVertical: 16,\n    borderBottomWidth: 1,\n    borderColor: '#313538',\n    shadowColor: '#000',\n    shadowOffset: {\n      width: 0,\n      height: 3,\n    },\n    shadowOpacity: 0.33,\n    shadowRadius: 3,\n    elevation: 8,\n  },\n  headerContent: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    gap: 14,\n    paddingHorizontal: '5%',\n    ...Platform.select({\n      web: {\n        width: '100%',\n        maxWidth: 960,\n        marginHorizontal: 'auto',\n      },\n    }),\n  },\n  title: {\n    color: 'white',\n    fontSize: 28,\n    fontWeight: 'bold',\n  },\n  scroll: {\n    paddingHorizontal: '5%',\n    paddingVertical: 16,\n    ...Platform.select({\n      ios: {\n        paddingBottom: 24,\n      },\n      web: {\n        width: '100%',\n        maxWidth: 960,\n        marginHorizontal: 'auto',\n        paddingBottom: 24,\n      },\n      default: {\n        paddingBottom: 12,\n      },\n    }),\n  },\n  itemContainer: {\n    borderWidth: 1,\n    borderColor: '#313538',\n    backgroundColor: '#151718',\n    borderRadius: 12,\n    marginBottom: 12,\n    overflow: 'hidden',\n  },\n  itemPressable: {\n    paddingHorizontal: INDENT,\n    paddingVertical: 16,\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    ...Platform.select({\n      web: {\n        transitionDuration: '100ms',\n      },\n    }),\n  },\n  filename: { color: 'white', fontSize: 20, marginLeft: 12 },\n  virtual: { textAlign: 'right', color: 'white' },\n  image: { width: 24, height: 24, resizeMode: 'contain', opacity: 0.6 },\n  headerIcon: {\n    width: 40,\n    height: 40,\n    backgroundColor: '#202425',\n    borderRadius: 8,\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n});\n"]}