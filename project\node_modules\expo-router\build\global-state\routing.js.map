{"version": 3, "file": "routing.js", "sourceRoot": "", "sources": ["../../src/global-state/routing.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmEA,4BAEC;AAED,wBAGC;AAED,4BAEC;AAED,oBAEC;AAED,0BAMC;AAED,8BAEC;AAED,0BAEC;AAED,gCAKC;AAED,wBAMC;AAED,8BAeC;AAED,gCAmBC;AAED,8BAQC;AAwBD,wBA6DC;AArPD,kCAAkC;AAClC,sDAAwC;AACxC,+CAAwC;AAExC,iDAAuC;AACvC,gEAMuC;AAEvC,8DAAuD;AACvD,uCAA0E;AAC1E,0CAA+C;AAG/C,sCAAoD;AAEpD,SAAS,aAAa;IACpB,IAAI,CAAC,oBAAK,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC;QACnC,MAAM,IAAI,KAAK,CACb,gKAAgK,CACjK,CAAC;IACJ,CAAC;AACH,CAAC;AAEY,QAAA,YAAY,GAAG;IAC1B,KAAK,EAAE,EAAwB;IAC/B,WAAW,EAAE,IAAI,GAAG,EAAc;IAClC,SAAS,CAAC,QAAoB;QAC5B,oBAAY,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACvC,OAAO,GAAG,EAAE;YACV,oBAAY,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC5C,CAAC,CAAC;IACJ,CAAC;IACD,QAAQ;QACN,OAAO,oBAAY,CAAC,KAAK,CAAC;IAC5B,CAAC;IACD,GAAG,CAAC,MAAwB;QAC1B,mCAAmC;QACnC,IAAI,oBAAY,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpC,oBAAY,CAAC,KAAK,GAAG,EAAE,CAAC;QAC1B,CAAC;QAED,oBAAY,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAChC,KAAK,MAAM,QAAQ,IAAI,oBAAY,CAAC,WAAW,EAAE,CAAC;YAChD,QAAQ,EAAE,CAAC;QACb,CAAC;IACH,CAAC;IACD,GAAG;QACD,MAAM,KAAK,GAAG,oBAAY,CAAC,KAAK,CAAC;QACjC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,oBAAK,CAAC,aAAa,EAAE,CAAC;YAC/C,OAAO;QACT,CAAC;QAED,oBAAY,CAAC,KAAK,GAAG,EAAE,CAAC;QACxB,KAAK,MAAM,MAAM,IAAI,KAAK,EAAE,CAAC;YAC3B,oBAAK,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;CACF,CAAC;AAIF,SAAgB,QAAQ,CAAC,GAAS,EAAE,OAA2B;IAC7D,OAAO,MAAM,CAAC,IAAA,kBAAW,EAAC,GAAG,CAAC,EAAE,EAAE,GAAG,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC;AACrE,CAAC;AAED,SAAgB,MAAM;IACpB,yCAAyC;IACzC,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;AACzF,CAAC;AAED,SAAgB,QAAQ,CAAC,IAAU,EAAE,OAA2B;IAC9D,OAAO,MAAM,CAAC,IAAA,kBAAW,EAAC,IAAI,CAAC,EAAE,EAAE,GAAG,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC;AACrE,CAAC;AAED,SAAgB,IAAI,CAAC,GAAS,EAAE,OAA2B;IACzD,OAAO,MAAM,CAAC,IAAA,kBAAW,EAAC,GAAG,CAAC,EAAE,EAAE,GAAG,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;AACjE,CAAC;AAED,SAAgB,OAAO,CAAC,QAAgB,CAAC;IACvC,IAAI,IAAA,6BAAc,EAAC,KAAK,CAAC,EAAE,CAAC;QAC1B,OAAO;IACT,CAAC;IAED,oBAAY,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;AACxD,CAAC;AAED,SAAgB,SAAS,CAAC,IAAU,EAAE,OAA2B;IAC/D,OAAO,MAAM,CAAC,IAAA,kBAAW,EAAC,IAAI,CAAC,EAAE,EAAE,GAAG,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;AACpE,CAAC;AAED,SAAgB,OAAO,CAAC,GAAS,EAAE,OAA2B;IAC5D,OAAO,MAAM,CAAC,IAAA,kBAAW,EAAC,GAAG,CAAC,EAAE,EAAE,GAAG,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC;AACpE,CAAC;AAED,SAAgB,UAAU;IACxB,IAAI,IAAA,gCAAiB,GAAE,EAAE,CAAC;QACxB,OAAO;IACT,CAAC;IACD,oBAAY,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;AAC3C,CAAC;AAED,SAAgB,MAAM;IACpB,IAAI,IAAA,4BAAa,GAAE,EAAE,CAAC;QACpB,OAAO;IACT,CAAC;IACD,aAAa,EAAE,CAAC;IAChB,oBAAY,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;AACxC,CAAC;AAED,SAAgB,SAAS;IACvB,IAAI,YAAM,EAAE,CAAC;QACX,MAAM,IAAI,KAAK,CACb,+FAA+F,CAChG,CAAC;IACJ,CAAC;IACD,oEAAoE;IACpE,2EAA2E;IAC3E,8FAA8F;IAC9F,yEAAyE;IACzE,uCAAuC;IACvC,IAAI,CAAC,oBAAK,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC;QACnC,OAAO,KAAK,CAAC;IACf,CAAC;IACD,OAAO,oBAAK,CAAC,aAAa,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,KAAK,CAAC;AAC5D,CAAC;AAED,SAAgB,UAAU;IACxB,IAAI,YAAM,EAAE,CAAC;QACX,MAAM,IAAI,KAAK,CACb,gGAAgG,CACjG,CAAC;IACJ,CAAC;IACD,IAAI,KAAK,GAAG,oBAAK,CAAC,KAAK,CAAC;IAExB,sFAAsF;IACtF,OAAO,KAAK,EAAE,CAAC;QACb,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtD,OAAO,IAAI,CAAC;QACd,CAAC;QACD,IAAI,KAAK,CAAC,KAAK,KAAK,SAAS;YAAE,OAAO,KAAK,CAAC;QAE5C,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,KAAY,CAAC;IACpD,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAgB,SAAS,CACvB,SAA4E,EAAE;IAE9E,IAAI,IAAA,+BAAgB,EAAC,MAAM,CAAC,EAAE,CAAC;QAC7B,OAAO;IACT,CAAC;IACD,aAAa,EAAE,CAAC;IAChB,OAAO,CAAC,oBAAK,CAAC,aAAa,EAAE,OAAO,EAAE,SAAiB,CAAA,CAAC,MAAM,CAAC,CAAC;AAClE,CAAC;AAwBD,SAAgB,MAAM,CAAC,YAAkB,EAAE,UAAyB,EAAE;IACpE,YAAY,GAAG,OAAO,YAAY,IAAI,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,IAAA,kBAAW,EAAC,YAAY,CAAC,CAAC;IAC1F,IAAI,IAAI,GAA8B,YAAY,CAAC;IAEnD,IAAI,IAAA,+BAAgB,EAAC,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC;QACpC,OAAO;IACT,CAAC;IAED,IAAI,IAAA,0BAAoB,EAAC,IAAI,CAAC,EAAE,CAAC;QAC/B,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,uBAAQ,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC;YACnD,IAAI,GAAG,SAAS,IAAI,EAAE,CAAC;QACzB,CAAC;QAED,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACtB,OAAO;IACT,CAAC;IAED,aAAa,EAAE,CAAC;IAChB,MAAM,aAAa,GAAG,oBAAK,CAAC,aAAa,CAAC,OAAO,CAAC;IAElD,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;QAC1B,MAAM,IAAI,KAAK,CACb,kFAAkF,CACnF,CAAC;IACJ,CAAC;IAED,IAAI,CAAC,oBAAK,CAAC,OAAO,EAAE,CAAC;QACnB,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;IAC3E,CAAC;IAED,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC;QACpC,aAAa,CAAC,MAAM,EAAE,CAAC;QACvB,OAAO;IACT,CAAC;IAED,MAAM,SAAS,GAAG,aAAa,CAAC,YAAY,EAAE,CAAC;IAE/C,IAAI,GAAG,IAAA,oCAA6B,EAAC,IAAI,EAAE,oBAAK,CAAC,YAAY,EAAE,EAAE,OAAO,CAAC,CAAC;IAC1E,IAAI,GAAG,IAAA,mCAAc,EAAC,IAAI,EAAE,oBAAK,CAAC,SAAS,CAAC,CAAC;IAE7C,+FAA+F;IAC/F,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,OAAO;IACT,CAAC;IAED,MAAM,KAAK,GAAG,oBAAK,CAAC,OAAO,CAAC,gBAAiB,CAAC,IAAI,EAAE,oBAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAE1E,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACxC,OAAO,CAAC,KAAK,CAAC,kEAAkE,GAAG,IAAI,CAAC,CAAC;QACzF,OAAO;IACT,CAAC;IAED,oBAAY,CAAC,GAAG,CACd,iBAAiB,CACf,KAAK,EACL,SAAS,EACT,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,UAAU,EAClB,OAAO,CAAC,mBAAmB,CAC5B,CACF,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CACxB,WAAwB,EACxB,eAAgC,EAChC,IAAI,GAAG,UAAU,EACjB,UAAoB,EACpB,QAA0B;IAE1B;;;;;;;;;;;;;OAaG;IACH,IAAI,gBAA+C,CAAC;IAEpD,4GAA4G;IAC5G,OAAO,WAAW,IAAI,eAAe,EAAE,CAAC;QACtC,MAAM,UAAU,GAAG,eAAe,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAEjE,gBAAgB,GAAG,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAErE,MAAM,UAAU,GAAQ,gBAAgB,CAAC,KAAK,CAAC;QAC/C,MAAM,mBAAmB,GAAG,UAAU,CAAC,KAAK,CAAC;QAE7C,MAAM,WAAW,GAAG,IAAA,2BAAgB,EAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAE5D,MAAM,+BAA+B,GACnC,gBAAgB,CAAC,IAAI,KAAK,UAAU,CAAC,IAAI;YACzC,CAAC,UAAU;YACX,CAAC,mBAAmB;YACpB,CAAC,WAAW;gBACV,wFAAwF;gBACxF,gBAAgB,CAAC,MAAM,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,UAAU,CAAC,MAAM,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;QAE3F,IAAI,+BAA+B,EAAE,CAAC;YACpC,MAAM;QACR,CAAC;QAED,WAAW,GAAG,UAAU,CAAC;QACzB,eAAe,GAAG,mBAAsC,CAAC;IAC3D,CAAC;IAED;;;OAGG;IACH,MAAM,WAAW,GAAwB,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;IACxD,IAAI,OAAO,GAAG,WAAW,CAAC;IAC1B,IAAI,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAE5B,+EAA+E;IAC/E,OAAO,gBAAgB,EAAE,CAAC;QACxB,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,GAAG,OAAO,CAAC,MAAM,EAAE,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,CAAC;QACzE,wCAAwC;QACxC,OAAO,CAAC,MAAM,GAAG,gBAAgB,CAAC,IAAI,CAAC;QACvC,yDAAyD;QACzD,OAAO,CAAC,MAAM,GAAG,EAAE,GAAG,MAAM,EAAE,CAAC;QAE/B,8DAA8D;QAC9D,OAAO,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAEhC,iCAAiC;QACjC,uFAAuF;QACvF,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;QACzB,MAAM,GAAG,OAAO,CAAC;QAEjB,gBAAgB,GAAG,gBAAgB,CAAC,KAAK,EAAE,MAAM,CAAC,gBAAgB,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC/F,CAAC;IAED,IAAI,IAAI,KAAK,MAAM,IAAI,eAAe,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QACxD,IAAI,GAAG,UAAU,CAAC;IACpB,CAAC;SAAM,IAAI,eAAe,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;QAC/C,IAAI,GAAG,SAAS,CAAC;IACnB,CAAC;SAAM,IAAI,IAAI,KAAK,SAAS,IAAI,eAAe,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;QACnE,IAAI,GAAG,SAAS,CAAC;IACnB,CAAC;IAED,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;QAC7B,IAAI,WAAW,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YAC/B,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;gBAC1C,OAAO,CAAC,IAAI,CAAC,0EAA0E,CAAC,CAAC;YAC3F,CAAC;QACH,CAAC;QACD;;;;;;;;WAQG;QACH,WAAW,CAAC,MAAM,CAAC,OAAO,GAAG,CAAC,UAAU,CAAC;IAC3C,CAAC;IAED,OAAO;QACL,IAAI;QACJ,MAAM,EAAE,eAAe,CAAC,GAAG;QAC3B,OAAO,EAAE;YACP,wBAAwB;YACxB,IAAI,EAAE,WAAW,CAAC,MAAM;YACxB,MAAM,EAAE,WAAW,CAAC,MAAM;YAC1B,QAAQ;SACT;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["import { NavigationAction, type NavigationState, PartialRoute } from '@react-navigation/native';\nimport { IS_DOM } from 'expo/dom';\nimport * as Linking from 'expo-linking';\nimport { Platform } from 'react-native';\n\nimport { store } from './router-store';\nimport {\n  emitDomDismiss,\n  emitDomDismissAll,\n  emitDomGoBack,\n  emitDomLinkEvent,\n  emitDomSetParams,\n} from '../domComponents/emitDomEvent';\nimport { ResultState } from '../fork/getStateFromPath';\nimport { applyRedirects } from '../getRoutesRedirects';\nimport { resolveHref, resolveHrefStringWithSegments } from '../link/href';\nimport { matchDynamicName } from '../matchers';\nimport { Href } from '../types';\nimport { SingularOptions } from '../useScreens';\nimport { shouldLinkExternally } from '../utils/url';\n\nfunction assertIsReady() {\n  if (!store.navigationRef.isReady()) {\n    throw new Error(\n      'Attempted to navigate before mounting the Root Layout component. Ensure the Root Layout component is rendering a Slot, or other navigator on the first render.'\n    );\n  }\n}\n\nexport const routingQueue = {\n  queue: [] as NavigationAction[],\n  subscribers: new Set<() => void>(),\n  subscribe(callback: () => void) {\n    routingQueue.subscribers.add(callback);\n    return () => {\n      routingQueue.subscribers.delete(callback);\n    };\n  },\n  snapshot() {\n    return routingQueue.queue;\n  },\n  add(action: NavigationAction) {\n    // Reset the identity of the queue.\n    if (routingQueue.queue.length === 0) {\n      routingQueue.queue = [];\n    }\n\n    routingQueue.queue.push(action);\n    for (const callback of routingQueue.subscribers) {\n      callback();\n    }\n  },\n  run() {\n    const queue = routingQueue.queue;\n    if (queue.length === 0 || !store.navigationRef) {\n      return;\n    }\n\n    routingQueue.queue = [];\n    for (const action of queue) {\n      store.navigationRef.dispatch(action);\n    }\n  },\n};\n\nexport type NavigationOptions = Omit<LinkToOptions, 'event'>;\n\nexport function navigate(url: Href, options?: NavigationOptions) {\n  return linkTo(resolveHref(url), { ...options, event: 'NAVIGATE' });\n}\n\nexport function reload() {\n  // TODO(EvanBacon): add `reload` support.\n  throw new Error('The reload method is not implemented in the client-side router yet.');\n}\n\nexport function prefetch(href: Href, options?: NavigationOptions) {\n  return linkTo(resolveHref(href), { ...options, event: 'PRELOAD' });\n}\n\nexport function push(url: Href, options?: NavigationOptions) {\n  return linkTo(resolveHref(url), { ...options, event: 'PUSH' });\n}\n\nexport function dismiss(count: number = 1) {\n  if (emitDomDismiss(count)) {\n    return;\n  }\n\n  routingQueue.add({ type: 'POP', payload: { count } });\n}\n\nexport function dismissTo(href: Href, options?: NavigationOptions) {\n  return linkTo(resolveHref(href), { ...options, event: 'POP_TO' });\n}\n\nexport function replace(url: Href, options?: NavigationOptions) {\n  return linkTo(resolveHref(url), { ...options, event: 'REPLACE' });\n}\n\nexport function dismissAll() {\n  if (emitDomDismissAll()) {\n    return;\n  }\n  routingQueue.add({ type: 'POP_TO_TOP' });\n}\n\nexport function goBack() {\n  if (emitDomGoBack()) {\n    return;\n  }\n  assertIsReady();\n  routingQueue.add({ type: 'GO_BACK' });\n}\n\nexport function canGoBack(): boolean {\n  if (IS_DOM) {\n    throw new Error(\n      'canGoBack imperative method is not supported. Pass the property to the DOM component instead.'\n    );\n  }\n  // Return a default value here if the navigation hasn't mounted yet.\n  // This can happen if the user calls `canGoBack` from the Root Layout route\n  // before mounting a navigator. This behavior exists due to React Navigation being dynamically\n  // constructed at runtime. We can get rid of this in the future if we use\n  // the static configuration internally.\n  if (!store.navigationRef.isReady()) {\n    return false;\n  }\n  return store.navigationRef?.current?.canGoBack() ?? false;\n}\n\nexport function canDismiss(): boolean {\n  if (IS_DOM) {\n    throw new Error(\n      'canDismiss imperative method is not supported. Pass the property to the DOM component instead.'\n    );\n  }\n  let state = store.state;\n\n  // Keep traversing down the state tree until we find a stack navigator that we can pop\n  while (state) {\n    if (state.type === 'stack' && state.routes.length > 1) {\n      return true;\n    }\n    if (state.index === undefined) return false;\n\n    state = state.routes?.[state.index]?.state as any;\n  }\n\n  return false;\n}\n\nexport function setParams(\n  params: Record<string, undefined | string | number | (string | number)[]> = {}\n) {\n  if (emitDomSetParams(params)) {\n    return;\n  }\n  assertIsReady();\n  return (store.navigationRef?.current?.setParams as any)(params);\n}\n\nexport type LinkToOptions = {\n  event?: string;\n\n  /**\n   * Relative URL references are either relative to the directory or the document. By default, relative paths are relative to the document.\n   * @see: [MDN's documentation on Resolving relative references to a URL](https://developer.mozilla.org/en-US/docs/Web/API/URL_API/Resolving_relative_references).\n   */\n  relativeToDirectory?: boolean;\n\n  /**\n   * Include the anchor when navigating to a new navigator\n   */\n  withAnchor?: boolean;\n\n  /**\n   * When navigating in a Stack, remove all screen from the history that match the singular condition\n   *\n   * If used with `push`, the history will be filtered even if no navigation occurs.\n   */\n  dangerouslySingular?: SingularOptions;\n};\n\nexport function linkTo(originalHref: Href, options: LinkToOptions = {}) {\n  originalHref = typeof originalHref == 'string' ? originalHref : resolveHref(originalHref);\n  let href: string | undefined | null = originalHref;\n\n  if (emitDomLinkEvent(href, options)) {\n    return;\n  }\n\n  if (shouldLinkExternally(href)) {\n    if (href.startsWith('//') && Platform.OS !== 'web') {\n      href = `https:${href}`;\n    }\n\n    Linking.openURL(href);\n    return;\n  }\n\n  assertIsReady();\n  const navigationRef = store.navigationRef.current;\n\n  if (navigationRef == null) {\n    throw new Error(\n      \"Couldn't find a navigation object. Is your component inside NavigationContainer?\"\n    );\n  }\n\n  if (!store.linking) {\n    throw new Error('Attempted to link to route when no routes are present');\n  }\n\n  if (href === '..' || href === '../') {\n    navigationRef.goBack();\n    return;\n  }\n\n  const rootState = navigationRef.getRootState();\n\n  href = resolveHrefStringWithSegments(href, store.getRouteInfo(), options);\n  href = applyRedirects(href, store.redirects);\n\n  // If the href is undefined, it means that the redirect has already been handled the navigation\n  if (!href) {\n    return;\n  }\n\n  const state = store.linking.getStateFromPath!(href, store.linking.config);\n\n  if (!state || state.routes.length === 0) {\n    console.error('Could not generate a valid navigation state for the given path: ' + href);\n    return;\n  }\n\n  routingQueue.add(\n    getNavigateAction(\n      state,\n      rootState,\n      options.event,\n      options.withAnchor,\n      options.dangerouslySingular\n    )\n  );\n}\n\nfunction getNavigateAction(\n  actionState: ResultState,\n  navigationState: NavigationState,\n  type = 'NAVIGATE',\n  withAnchor?: boolean,\n  singular?: SingularOptions\n) {\n  /**\n   * We need to find the deepest navigator where the action and current state diverge, If they do not diverge, the\n   * lowest navigator is the target.\n   *\n   * By default React Navigation will target the current navigator, but this doesn't work for all actions\n   * For example:\n   *  - /deeply/nested/route -> /top-level-route the target needs to be the top-level navigator\n   *  - /stack/nestedStack/page -> /stack1/nestedStack/other-page needs to target the nestedStack navigator\n   *\n   * This matching needs to done by comparing the route names and the dynamic path, for example\n   * - /1/page -> /2/anotherPage needs to target the /[id] navigator\n   *\n   * Other parameters such as search params and hash are not evaluated.\n   */\n  let actionStateRoute: PartialRoute<any> | undefined;\n\n  // Traverse the state tree comparing the current state and the action state until we find where they diverge\n  while (actionState && navigationState) {\n    const stateRoute = navigationState.routes[navigationState.index];\n\n    actionStateRoute = actionState.routes[actionState.routes.length - 1];\n\n    const childState: any = actionStateRoute.state;\n    const nextNavigationState = stateRoute.state;\n\n    const dynamicName = matchDynamicName(actionStateRoute.name);\n\n    const didActionAndCurrentStateDiverge =\n      actionStateRoute.name !== stateRoute.name ||\n      !childState ||\n      !nextNavigationState ||\n      (dynamicName &&\n        // @ts-expect-error: TODO(@kitten): This isn't properly typed, so the index access fails\n        actionStateRoute.params?.[dynamicName.name] !== stateRoute.params?.[dynamicName.name]);\n\n    if (didActionAndCurrentStateDiverge) {\n      break;\n    }\n\n    actionState = childState;\n    navigationState = nextNavigationState as NavigationState;\n  }\n\n  /*\n   * We found the target navigator, but the payload is in the incorrect format\n   * We need to convert the action state to a payload that can be dispatched\n   */\n  const rootPayload: Record<string, any> = { params: {} };\n  let payload = rootPayload;\n  let params = payload.params;\n\n  // The root level of payload is a bit weird, its params are in the child object\n  while (actionStateRoute) {\n    Object.assign(params, { ...payload.params, ...actionStateRoute.params });\n    // Assign the screen name to the payload\n    payload.screen = actionStateRoute.name;\n    // Merge the params, ensuring that we create a new object\n    payload.params = { ...params };\n\n    // Params don't include the screen, thats a separate attribute\n    delete payload.params['screen'];\n\n    // Continue down the payload tree\n    // Initially these values are separate, but React Nav merges them after the first layer\n    payload = payload.params;\n    params = payload;\n\n    actionStateRoute = actionStateRoute.state?.routes[actionStateRoute.state?.routes.length - 1];\n  }\n\n  if (type === 'PUSH' && navigationState.type !== 'stack') {\n    type = 'NAVIGATE';\n  } else if (navigationState.type === 'expo-tab') {\n    type = 'JUMP_TO';\n  } else if (type === 'REPLACE' && navigationState.type === 'drawer') {\n    type = 'JUMP_TO';\n  }\n\n  if (withAnchor !== undefined) {\n    if (rootPayload.params.initial) {\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn(`The parameter 'initial' is a reserved parameter name in React Navigation`);\n      }\n    }\n    /*\n     * The logic for initial can seen backwards depending on your perspective\n     *   True: The initialRouteName is not loaded. The incoming screen is the initial screen (default)\n     *   False: The initialRouteName is loaded. THe incoming screen is placed after the initialRouteName\n     *\n     * withAnchor flips the perspective.\n     *   True: You want the initialRouteName to load.\n     *   False: You do not want the initialRouteName to load.\n     */\n    rootPayload.params.initial = !withAnchor;\n  }\n\n  return {\n    type,\n    target: navigationState.key,\n    payload: {\n      // key: rootPayload.key,\n      name: rootPayload.screen,\n      params: rootPayload.params,\n      singular,\n    },\n  };\n}\n"]}