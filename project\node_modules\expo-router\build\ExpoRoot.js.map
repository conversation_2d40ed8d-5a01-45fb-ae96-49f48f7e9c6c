{"version": 3, "file": "ExpoRoot.js", "sourceRoot": "", "sources": ["../src/ExpoRoot.tsx"], "names": [], "mappings": ";AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwDb,4BAqBC;AA3ED,qDAKkC;AAClC,+CAA6F;AAC7F,+CAAmE;AACnE,mFAAkE;AAElE,2CAAiD;AACjD,yFAAsF;AACtF,oEAAgG;AAEhG,8DAA8D;AAC9D,gFAAwF;AACxF,8DAA2D;AAC3D,qDAAwD;AACxD,6CAAsC;AAEtC,iDAAiE;AACjE,6DAA+C;AAgB/C,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM,CAAC;AAElD,MAAM,eAAe,GACnB,uBAAQ,CAAC,EAAE,KAAK,KAAK,IAAI,SAAS;IAChC,CAAC,CAAC;QACE,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;QAC1C,MAAM,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;KACjD;IACH,CAAC,CAAC,SAAS,CAAC;AAEhB,MAAM,aAAa,GAAG;IACpB,OAAO,EAAE,KAAK;CACf,CAAC;AAEF;;GAEG;AACH,SAAgB,QAAQ,CAAC,EAAE,OAAO,EAAE,aAAa,GAAG,gBAAQ,EAAE,GAAG,KAAK,EAAiB;IACrF;;;;OAIG;IACH,MAAM,OAAO,GAAG,CAAC,EAAE,QAAQ,EAAqB,EAAE,EAAE;QAClD,OAAO,CACL,CAAC,aAAa,CACZ;QAAA,CAAC,iDAAgB;QACf,cAAc;QACd,cAAc,CAAC,CAAC,eAAe,CAAC,CAChC;UAAA,CAAC,wGAAwG,CACzG;UAAA,CAAC,wCAA4B,IAAI,CAAC,aAAa,CAAC,AAAD,EAAG,CAClD;UAAA,CAAC,QAAQ,CACX;QAAA,EAAE,iDAAgB,CACpB;MAAA,EAAE,aAAa,CAAC,CACjB,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO,CAAC,gBAAgB,CAAC,IAAI,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAG,CAAC;AAC3D,CAAC;AAED,SAAS,aAAa;IACpB,OAAO,CAAC,wBAAS,CAAC,QAAQ,CAAC,CAAC,IAAA,6BAAc,GAAE,KAAK,OAAO,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,eAAe,CAAC,EAAG,CAAC;AAClG,CAAC;AAED,MAAM,UAAU,GACd,uBAAQ,CAAC,EAAE,KAAK,KAAK,IAAI,OAAO,MAAM,KAAK,WAAW;IACpD,CAAC,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;IAC/B,CAAC,CAAC,SAAS,CAAC;AAEhB,SAAS,gBAAgB,CAAC,EACxB,OAAO,EACP,QAAQ,EAAE,eAAe,GAAG,UAAU,EACtC,OAAO,EAAE,gBAAgB,GAAG,gBAAQ,EACpC,OAAO,GAAG,EAAE,GACE;IACd,kFAAkF;IAClF,yDAAyD;IACzD,6CAA6C;IAC7C,MAAM,aAAa,GAAG,IAAA,eAAO,EAAC,GAAG,EAAE;QACjC,IAAI,WAAW,GAAsB,EAAE,CAAC;QAExC,IAAI,eAAe,YAAY,GAAG,EAAE,CAAC;YACnC,WAAW,GAAG;gBACZ,QAAQ,EAAE;oBACR,QAAQ,EAAE,eAAe,CAAC,QAAQ,GAAG,eAAe,CAAC,IAAI;oBACzD,MAAM,EAAE,eAAe,CAAC,MAAM;iBAC/B;aACF,CAAC;QACJ,CAAC;aAAM,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE,CAAC;YAC/C,uEAAuE;YACvE,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,eAAe,EAAE,yBAAyB,CAAC,CAAC;YAChE,WAAW,GAAG;gBACZ,QAAQ,EAAE;oBACR,QAAQ,EAAE,GAAG,CAAC,QAAQ;oBACtB,MAAM,EAAE,GAAG,CAAC,MAAM;iBACnB;aACF,CAAC;QACJ,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP;;;OAGG;IACH,MAAM,SAAS,GAAG,aAAa,CAAC,QAAQ;QACtC,CAAC,CAAC,GAAG,aAAa,CAAC,QAAQ,CAAC,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC,MAAM,EAAE;QACtE,CAAC,CAAC,SAAS,CAAC;IAEd,MAAM,KAAK,GAAG,IAAA,uBAAQ,EAAC,OAAO,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;IAEpD,IAAA,qDAAyB,GAAE,CAAC;IAE5B,IAAI,KAAK,CAAC,kBAAkB,EAAE,EAAE,CAAC;QAC/B,YAAY,CAAC,SAAS,EAAE,CAAC;QACzB,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC3C,MAAM,QAAQ,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC,QAAQ,CAAC;YACxD,OAAO,CACL,CAAC,gBAAgB,CACf;UAAA,CAAC,QAAQ,CAAC,AAAD,EACX;QAAA,EAAE,gBAAgB,CAAC,CACpB,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,qDAAqD;YACrD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,OAAO,CACL,CAAC,2BAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAClC;MAAA,CAAC,yCAA2B,CAC1B,GAAG,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC,CACzB,YAAY,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAC1B,OAAO,CAAC,CAAC,KAAK,CAAC,OAA8B,CAAC,CAC9C,iBAAiB,CAAC,CAAC,iBAAiB,CAAC,CACrC,aAAa,CAAC,CAAC,aAAa,CAAC,CAC7B,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CACvB;QAAA,CAAC,qCAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,aAAa,CAAC,CAC3C;UAAA,CAAC,gBAAgB,CACf;YAAA,CAAC,qCAAoB,CAAC,AAAD,EACrB;YAAA,CAAC,OAAO,CAAC,AAAD,EACV;UAAA,EAAE,gBAAgB,CACpB;QAAA,EAAE,qCAAa,CAAC,QAAQ,CAC1B;MAAA,EAAE,yCAA2B,CAC/B;IAAA,EAAE,2BAAY,CAAC,QAAQ,CAAC,CACzB,CAAC;AACJ,CAAC;AAED,SAAS,OAAO;IACd,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,iBAAiB,EAAE,GAAG,IAAA,6BAAoB,EAAC,oBAAW,EAAE;QAClF,QAAQ,EAAE,CAAC,mBAAM,CAAC,IAAI,CAAC,CAAC,8BAAkB,CAAC,CAAC,SAAS,CAAC,CAAC,oBAAK,CAAC,aAAa,CAAC,EAAG;QAC9E,EAAE,EAAE,8BAAkB;KACvB,CAAC,CAAC;IAEH,OAAO,CAAC,iBAAiB,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,iBAAiB,CAAC,CAAC;AAC5F,CAAC;AAED,IAAI,iBAAqD,CAAC;AAE1D,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;IAC1C,iBAAiB,GAAG,CAAC,MAAwB,EAAE,EAAE;QAC/C,MAAM,OAAO,GAAoC,MAAM,CAAC,OAAO,CAAC;QAEhE,IAAI,OAAO,GAAG,eAAe,MAAM,CAAC,IAAI,IACtC,OAAO,CAAC,CAAC,CAAC,iBAAiB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAChE,oCAAoC,CAAC;QAErC,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;YACpB,KAAK,UAAU,CAAC;YAChB,KAAK,MAAM,CAAC;YACZ,KAAK,SAAS,CAAC;YACf,KAAK,SAAS;gBACZ,IAAI,OAAO,EAAE,IAAI,EAAE,CAAC;oBAClB,OAAO,IAAI,kCAAkC,OAAO,CAAC,IAAI,IAAI,CAAC;gBAChE,CAAC;qBAAM,CAAC;oBACN,OAAO,IAAI,gFAAgF,CAAC;gBAC9F,CAAC;gBAED,MAAM;YACR,KAAK,SAAS,CAAC;YACf,KAAK,KAAK,CAAC;YACX,KAAK,YAAY;gBACf,OAAO,IAAI,wCAAwC,CAAC;gBACpD,MAAM;YACR,KAAK,aAAa,CAAC;YACnB,KAAK,cAAc,CAAC;YACpB,KAAK,eAAe;gBAClB,OAAO,IAAI,+CAA+C,CAAC;gBAC3D,MAAM;QACV,CAAC;QAED,OAAO,IAAI,0EAA0E,CAAC;QAEtF,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;QAC3B,CAAC;QACD,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IACzB,CAAC,CAAC;AACJ,CAAC;KAAM,CAAC;IACN,iBAAiB,GAAG,cAAa,CAAC,CAAC;AACrC,CAAC", "sourcesContent": ["'use client';\n\nimport {\n  LinkingOptions,\n  NavigationAction,\n  StackRouter,\n  useNavigationBuilder,\n} from '@react-navigation/native';\nimport React, { type PropsWithChildren, Fragment, type ComponentType, useMemo } from 'react';\nimport { StatusBar, useColorScheme, Platform } from 'react-native';\nimport { SafeAreaProvider } from 'react-native-safe-area-context';\n\nimport { INTERNAL_SLOT_NAME } from './constants';\nimport { useDomComponentNavigation } from './domComponents/useDomComponentNavigation';\nimport { NavigationContainer as UpstreamNavigationContainer } from './fork/NavigationContainer';\nimport { ExpoLinkingOptions } from './getLinkingConfig';\nimport { store, useStore } from './global-state/router-store';\nimport { ServerContext, ServerContextType } from './global-state/serverLocationContext';\nimport { StoreContext } from './global-state/storeContext';\nimport { ImperativeApiEmitter } from './imperative-api';\nimport { Screen } from './primitives';\nimport { RequireContext } from './types';\nimport { canOverrideStatusBarBehavior } from './utils/statusbar';\nimport * as SplashScreen from './views/Splash';\n\nexport type ExpoRootProps = {\n  context: RequireContext;\n  location?: URL | string;\n  wrapper?: ComponentType<PropsWithChildren>;\n  linking?: Partial<ExpoLinkingOptions>;\n};\n\nexport type NativeIntent = {\n  redirectSystemPath?: (event: {\n    path: string | null;\n    initial: boolean;\n  }) => Promise<string | null | undefined> | string | null | undefined;\n};\n\nconst isTestEnv = process.env.NODE_ENV === 'test';\n\nconst INITIAL_METRICS =\n  Platform.OS === 'web' || isTestEnv\n    ? {\n        frame: { x: 0, y: 0, width: 0, height: 0 },\n        insets: { top: 0, left: 0, right: 0, bottom: 0 },\n      }\n    : undefined;\n\nconst documentTitle = {\n  enabled: false,\n};\n\n/**\n * @hidden\n */\nexport function ExpoRoot({ wrapper: ParentWrapper = Fragment, ...props }: ExpoRootProps) {\n  /*\n   * Due to static rendering we need to wrap these top level views in second wrapper\n   * View's like <SafeAreaProvider /> generate a <div> so if the parent wrapper\n   * is a HTML document, we need to ensure its inside the <body>\n   */\n  const wrapper = ({ children }: PropsWithChildren) => {\n    return (\n      <ParentWrapper>\n        <SafeAreaProvider\n          // SSR support\n          initialMetrics={INITIAL_METRICS}>\n          {/* Users can override this by adding another StatusBar element anywhere higher in the component tree. */}\n          {canOverrideStatusBarBehavior && <AutoStatusBar />}\n          {children}\n        </SafeAreaProvider>\n      </ParentWrapper>\n    );\n  };\n\n  return <ContextNavigator {...props} wrapper={wrapper} />;\n}\n\nfunction AutoStatusBar() {\n  return <StatusBar barStyle={useColorScheme() === 'light' ? 'dark-content' : 'light-content'} />;\n}\n\nconst initialUrl =\n  Platform.OS === 'web' && typeof window !== 'undefined'\n    ? new URL(window.location.href)\n    : undefined;\n\nfunction ContextNavigator({\n  context,\n  location: initialLocation = initialUrl,\n  wrapper: WrapperComponent = Fragment,\n  linking = {},\n}: ExpoRootProps) {\n  // location and linking.getInitialURL are both used to initialize the router state\n  //  - location is used on web and during static rendering\n  //  - linking.getInitialURL is used on native\n  const serverContext = useMemo(() => {\n    let contextType: ServerContextType = {};\n\n    if (initialLocation instanceof URL) {\n      contextType = {\n        location: {\n          pathname: initialLocation.pathname + initialLocation.hash,\n          search: initialLocation.search,\n        },\n      };\n    } else if (typeof initialLocation === 'string') {\n      // The initial location is a string, so we need to parse it into a URL.\n      const url = new URL(initialLocation, 'http://placeholder.base');\n      contextType = {\n        location: {\n          pathname: url.pathname,\n          search: url.search,\n        },\n      };\n    }\n\n    return contextType;\n  }, []);\n\n  /*\n   * The serverUrl is an initial URL used in server rendering environments.\n   * e.g Static renders, units tests, etc\n   */\n  const serverUrl = serverContext.location\n    ? `${serverContext.location.pathname}${serverContext.location.search}`\n    : undefined;\n\n  const store = useStore(context, linking, serverUrl);\n\n  useDomComponentNavigation();\n\n  if (store.shouldShowTutorial()) {\n    SplashScreen.hideAsync();\n    if (process.env.NODE_ENV === 'development') {\n      const Tutorial = require('./onboard/Tutorial').Tutorial;\n      return (\n        <WrapperComponent>\n          <Tutorial />\n        </WrapperComponent>\n      );\n    } else {\n      // Ensure tutorial styles are stripped in production.\n      return null;\n    }\n  }\n\n  return (\n    <StoreContext.Provider value={store}>\n      <UpstreamNavigationContainer\n        ref={store.navigationRef}\n        initialState={store.state}\n        linking={store.linking as LinkingOptions<any>}\n        onUnhandledAction={onUnhandledAction}\n        documentTitle={documentTitle}\n        onReady={store.onReady}>\n        <ServerContext.Provider value={serverContext}>\n          <WrapperComponent>\n            <ImperativeApiEmitter />\n            <Content />\n          </WrapperComponent>\n        </ServerContext.Provider>\n      </UpstreamNavigationContainer>\n    </StoreContext.Provider>\n  );\n}\n\nfunction Content() {\n  const { state, descriptors, NavigationContent } = useNavigationBuilder(StackRouter, {\n    children: <Screen name={INTERNAL_SLOT_NAME} component={store.rootComponent} />,\n    id: INTERNAL_SLOT_NAME,\n  });\n\n  return <NavigationContent>{descriptors[state.routes[0].key].render()}</NavigationContent>;\n}\n\nlet onUnhandledAction: (action: NavigationAction) => void;\n\nif (process.env.NODE_ENV !== 'production') {\n  onUnhandledAction = (action: NavigationAction) => {\n    const payload: Record<string, any> | undefined = action.payload;\n\n    let message = `The action '${action.type}'${\n      payload ? ` with payload ${JSON.stringify(action.payload)}` : ''\n    } was not handled by any navigator.`;\n\n    switch (action.type) {\n      case 'NAVIGATE':\n      case 'PUSH':\n      case 'REPLACE':\n      case 'JUMP_TO':\n        if (payload?.name) {\n          message += `\\n\\nDo you have a route named '${payload.name}'?`;\n        } else {\n          message += `\\n\\nYou need to pass the name of the screen to navigate to. This may be a bug.`;\n        }\n\n        break;\n      case 'GO_BACK':\n      case 'POP':\n      case 'POP_TO_TOP':\n        message += `\\n\\nIs there any screen to go back to?`;\n        break;\n      case 'OPEN_DRAWER':\n      case 'CLOSE_DRAWER':\n      case 'TOGGLE_DRAWER':\n        message += `\\n\\nIs your screen inside a Drawer navigator?`;\n        break;\n    }\n\n    message += `\\n\\nThis is a development-only warning and won't be shown in production.`;\n\n    if (process.env.NODE_ENV === 'test') {\n      throw new Error(message);\n    }\n    console.error(message);\n  };\n} else {\n  onUnhandledAction = function () {};\n}\n"]}