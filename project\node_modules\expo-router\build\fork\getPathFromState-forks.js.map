{"version": 3, "file": "getPathFromState-forks.js", "sourceRoot": "", "sources": ["../../src/fork/getPathFromState-forks.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBA,gDAOC;AAED,4CA+BC;AAED,gDAeC;AAED,sCAWC;AAED,0EA+EC;AAID,sCAEC;AA9KD,qDAAkG;AAClG,0DAA4C;AAG5C,0CAA+D;AAa/D,SAAgB,kBAAkB,CAA2B,EAC3D,qBAAqB,EACrB,cAAc,EACd,sBAAsB,EACtB,GAAG,OAAO,EACS;IACnB,IAAA,2BAAoB,EAAC,OAAO,CAAC,CAAC;AAChC,CAAC;AAED,SAAgB,gBAAgB,CAC9B,SAA8B,EAC9B,KAEC,EACD,SAA2B;IAE3B,6BAA6B;IAC7B,MAAM,aAAa,GAAG,MAAM,CAAC,WAAW,CACtC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;QACrD,IAAI,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,QAAQ,EAAE,CAAC;YACzC,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,OAAO;YACL;gBACE,GAAG;gBACH,SAAS,EAAE,CAAC,GAAG,CAAC;oBACd,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;oBACvB,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;wBACpB,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC;wBACnB,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aACpB;SACF,CAAC;IACJ,CAAC,CAAC,CACH,CAAC;IAEF,6EAA6E;IAC7E,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;IAExC,OAAO,aAAa,CAAC;AACvB,CAAC;AAED,SAAgB,kBAAkB,CAChC,IAAY,EACZ,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,aAAa,EAAuB;IAEpD,MAAM,KAAK,GAAG,WAAW,CAAC,SAAS,CAAC,aAAa,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;IAEpE,IAAI,KAAK,EAAE,CAAC;QACV,IAAI,IAAI,IAAI,KAAK,EAAE,CAAC;IACtB,CAAC;IAED,IAAI,IAAI,EAAE,CAAC;QACT,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;IACrB,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAgB,aAAa,CAC3B,IAAY,EACZ,UAA8B,OAAO,CAAC,GAAG,CAAC,aAAa;IAEvD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;QAC3C,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC;QACrE,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAgB,+BAA+B,CAAC,EAC9C,OAAO,EACP,KAAK,EACL,MAAM,EACN,cAAc,EACd,qBAAqB,EACrB,sBAAsB,GAAG,IAAI,EAC7B,gBAAgB,GAMjB;IACC,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAEpC,OAAO,QAAQ;SACZ,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QACZ,MAAM,IAAI,GAAG,IAAA,oBAAY,EAAC,CAAC,CAAC,CAAC;QAE7B,kFAAkF;QAClF,wCAAwC;QACxC,IAAI,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACtB,IAAI,qBAAqB,EAAE,CAAC;gBAC1B,IAAI,IAAI,KAAK,WAAW,EAAE,CAAC;oBACzB,OAAO,YAAY,CAAC;gBACtB,CAAC;gBAED,OAAO,OAAO,IAAI,GAAG,CAAC;YACxB,CAAC;iBAAM,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;gBACxB,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;oBAChC,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAChC,CAAC;gBACD,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;YACtB,CAAC;iBAAM,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAClE,OAAO,EAAE,CAAC;YACZ,CAAC;iBAAM,IAAI,CAAC,KAAK,YAAY,EAAE,CAAC;gBAC9B,OAAO,EAAE,CAAC;YACZ,CAAC;iBAAM,CAAC;gBACN,OAAO,KAAK,CAAC,IAAI,CAAC;YACpB,CAAC;QACH,CAAC;QAED,mEAAmE;QACnE,IAAI,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACtB,IAAI,qBAAqB,EAAE,CAAC;gBAC1B,OAAO,IAAI,IAAI,GAAG,CAAC;YACrB,CAAC;YACD,2EAA2E;YAC3E,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;YAC3B,IAAI,KAAK,KAAK,SAAS,IAAI,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC3C,OAAO;YACT,CAAC;YAED,OAAO,CAAC,sBAAsB,CAAC,CAAC,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,WAAW,CAAC;QACnF,CAAC;QAED,IAAI,CAAC,cAAc,IAAI,IAAA,yBAAc,EAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;YACjD,yDAAyD;YACzD,+DAA+D;YAC/D,oEAAoE;YACpE,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC9B,IAAI,gBAAgB,EAAE,CAAC;oBACrB,yDAAyD;oBACzD,IAAI,wBAAwB,CAAC,gBAAgB,CAAC,EAAE,CAAC;wBAC/C,OAAO,EAAE,CAAC;oBACZ,CAAC;oBACD,OAAO,sBAAsB;wBAC3B,CAAC,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,EAAE,gBAAgB,EAAE,IAAI,EAAE,CAAC;wBAChE,CAAC,CAAC,gBAAgB,CAAC;gBACvB,CAAC;YACH,CAAC;YACD,OAAO,EAAE,CAAC;QACZ,CAAC;QACD,0CAA0C;QAC1C,OAAO,sBAAsB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,EAAE,EAAE,gBAAgB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtF,CAAC,CAAC;SACD,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SACnB,IAAI,CAAC,GAAG,CAAC,CAAC;AACf,CAAC;AAEM,MAAM,YAAY,GAAG,CAAC,OAAe,EAAE,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AAApF,QAAA,YAAY,gBAAwE;AAEjG,SAAgB,aAAa,CAAC,CAAS;IACrC,OAAO,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAChD,CAAC;AAED,SAAS,wBAAwB,CAAC,OAAe;IAC/C,OAAO,CACL,OAAO,KAAK,OAAO,IAAI,IAAA,yBAAc,EAAC,OAAO,CAAC,IAAI,IAAI,IAAI,IAAA,2BAAgB,EAAC,OAAO,CAAC,IAAI,IAAI,CAC5F,CAAC;AACJ,CAAC;AAED,SAAS,gBAAgB,CAAC,GAAW,EAAE,EAAE,gBAAgB,GAAG,KAAK,EAAE,GAAG,EAAE;IACtE,gCAAgC;IAChC,mFAAmF;IACnF,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,iCAAiC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;IAEjG,IAAI,gBAAgB,EAAE,CAAC;QACrB,oBAAoB;QACpB,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IACtD,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC", "sourcesContent": ["import { validatePathConfig as RNValidatePathConfig, type Route } from '@react-navigation/native';\nimport * as queryString from 'query-string';\n\nimport type { Options, State, StringifyConfig } from './getPathFromState';\nimport { matchDynamicName, matchGroupName } from '../matchers';\n\nexport type ExpoOptions = {\n  preserveDynamicRoutes?: boolean;\n  preserveGroups?: boolean;\n  shouldEncodeURISegment?: boolean;\n};\n\nexport type ExpoConfigItem = {\n  // Used as fallback for groups\n  initialRouteName?: string;\n};\n\nexport function validatePathConfig<ParamList extends object>({\n  preserveDynamicRoutes,\n  preserveGroups,\n  shouldEncodeURISegment,\n  ...options\n}: Options<ParamList>) {\n  RNValidatePathConfig(options);\n}\n\nexport function fixCurrentParams(\n  allParams: Record<string, any>,\n  route: Route<string> & {\n    state?: State;\n  },\n  stringify?: StringifyConfig\n) {\n  // Better handle array params\n  const currentParams = Object.fromEntries(\n    Object.entries(route.params!).flatMap(([key, value]) => {\n      if (key === 'screen' || key === 'params') {\n        return [];\n      }\n\n      return [\n        [\n          key,\n          stringify?.[key]\n            ? stringify[key](value)\n            : Array.isArray(value)\n              ? value.map(String)\n              : String(value),\n        ],\n      ];\n    })\n  );\n\n  // We always assign params, as non pattern routes may still have query params\n  Object.assign(allParams, currentParams);\n\n  return currentParams;\n}\n\nexport function appendQueryAndHash(\n  path: string,\n  { '#': hash, ...focusedParams }: Record<string, any>\n) {\n  const query = queryString.stringify(focusedParams, { sort: false });\n\n  if (query) {\n    path += `?${query}`;\n  }\n\n  if (hash) {\n    path += `#${hash}`;\n  }\n\n  return path;\n}\n\nexport function appendBaseUrl(\n  path: string,\n  baseUrl: string | undefined = process.env.EXPO_BASE_URL\n) {\n  if (process.env.NODE_ENV !== 'development') {\n    if (baseUrl) {\n      return `/${baseUrl.replace(/^\\/+/, '').replace(/\\/$/, '')}${path}`;\n    }\n  }\n\n  return path;\n}\n\nexport function getPathWithConventionsCollapsed({\n  pattern,\n  route,\n  params,\n  preserveGroups,\n  preserveDynamicRoutes,\n  shouldEncodeURISegment = true,\n  initialRouteName,\n}: ExpoOptions & {\n  pattern: string;\n  route: Route<any>;\n  params: Record<string, any>;\n  initialRouteName?: string;\n}) {\n  const segments = pattern.split('/');\n\n  return segments\n    .map((p, i) => {\n      const name = getParamName(p);\n\n      // Showing the route name seems ok, though whatever we show here will be incorrect\n      // Since the page doesn't actually exist\n      if (p.startsWith('*')) {\n        if (preserveDynamicRoutes) {\n          if (name === 'not-found') {\n            return '+not-found';\n          }\n\n          return `[...${name}]`;\n        } else if (params[name]) {\n          if (Array.isArray(params[name])) {\n            return params[name].join('/');\n          }\n          return params[name];\n        } else if (route.name.startsWith('[') && route.name.endsWith(']')) {\n          return '';\n        } else if (p === '*not-found') {\n          return '';\n        } else {\n          return route.name;\n        }\n      }\n\n      // If the path has a pattern for a param, put the param in the path\n      if (p.startsWith(':')) {\n        if (preserveDynamicRoutes) {\n          return `[${name}]`;\n        }\n        // Optional params without value assigned in route.params should be ignored\n        const value = params[name];\n        if (value === undefined && p.endsWith('?')) {\n          return;\n        }\n\n        return (shouldEncodeURISegment ? encodeURISegment(value) : value) ?? 'undefined';\n      }\n\n      if (!preserveGroups && matchGroupName(p) != null) {\n        // When the last part is a group it could be a shared URL\n        // if the route has an initialRouteName defined, then we should\n        // use that as the component path as we can assume it will be shown.\n        if (segments.length - 1 === i) {\n          if (initialRouteName) {\n            // Return an empty string if the init route is ambiguous.\n            if (segmentMatchesConvention(initialRouteName)) {\n              return '';\n            }\n            return shouldEncodeURISegment\n              ? encodeURISegment(initialRouteName, { preserveBrackets: true })\n              : initialRouteName;\n          }\n        }\n        return '';\n      }\n      // Preserve dynamic syntax for rehydration\n      return shouldEncodeURISegment ? encodeURISegment(p, { preserveBrackets: true }) : p;\n    })\n    .map((v) => v ?? '')\n    .join('/');\n}\n\nexport const getParamName = (pattern: string) => pattern.replace(/^[:*]/, '').replace(/\\?$/, '');\n\nexport function isDynamicPart(p: string) {\n  return p.startsWith(':') || p.startsWith('*');\n}\n\nfunction segmentMatchesConvention(segment: string): boolean {\n  return (\n    segment === 'index' || matchGroupName(segment) != null || matchDynamicName(segment) != null\n  );\n}\n\nfunction encodeURISegment(str: string, { preserveBrackets = false } = {}) {\n  // Valid characters according to\n  // https://datatracker.ietf.org/doc/html/rfc3986#section-3.3 (see pchar definition)\n  str = String(str).replace(/[^A-Za-z0-9\\-._~!$&'()*+,;=:@]/g, (char) => encodeURIComponent(char));\n\n  if (preserveBrackets) {\n    // Preserve brackets\n    str = str.replace(/%5B/g, '[').replace(/%5D/g, ']');\n  }\n  return str;\n}\n"]}