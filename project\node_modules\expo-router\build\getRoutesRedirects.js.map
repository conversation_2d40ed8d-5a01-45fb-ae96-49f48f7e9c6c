{"version": 3, "file": "getRoutesRedirects.js", "sourceRoot": "", "sources": ["../src/getRoutesRedirects.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,wCA2BC;AAED,8CAiCC;AAED,0CAoBC;AAED,wDAeC;AA7GD,sDAAwC;AACxC,iCAAsC;AAEtC,0EAA0D;AAG1D,yCAA8C;AAE9C,SAAgB,cAAc,CAC5B,GAA8B,EAC9B,SAAuC;IAEvC,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,CAAC,SAAS,EAAE,CAAC;QAC1C,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,OAAO,GAAG,IAAA,kCAAS,EAAC,GAAG,CAAC,CAAC;IAC/B,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IAElE,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,OAAO,GAAG,CAAC;IACb,CAAC;IAED,4CAA4C;IAC5C,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;QAChB,IAAI,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;QACnC,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;YAC3D,IAAI,GAAG,SAAS,IAAI,EAAE,CAAC;QACzB,CAAC;QAED,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACtB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,cAAc,CAAC,eAAe,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;AACtE,CAAC;AAED,SAAgB,iBAAiB,CAAC,KAAa;IAC7C,OAAO;QACL,OAAO,EAAE,SAAS,iBAAiB;YACjC,2DAA2D;YAC3D,iEAAiE;YACjE,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,qBAAqB,EAAE,CAAC;YAE1D,4EAA4E;YAC5E,IAAI,IAAI,GAAG,KAAK;iBACb,KAAK,CAAC,GAAG,CAAC;iBACV,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBACZ,MAAM,WAAW,GAAG,IAAA,2BAAgB,EAAC,IAAI,CAAC,CAAC;gBAC3C,IAAI,CAAC,WAAW,EAAE,CAAC;oBACjB,OAAO,IAAI,CAAC;gBACd,CAAC;qBAAM,CAAC;oBACN,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;oBACvC,OAAO,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;oBAChC,OAAO,KAAK,CAAC;gBACf,CAAC;YACH,CAAC,CAAC;iBACD,MAAM,CAAC,OAAO,CAAC;iBACf,IAAI,CAAC,GAAG,CAAC,CAAC;YAEb,2CAA2C;YAC3C,MAAM,WAAW,GAAG,IAAI,eAAe,CAAC,MAA6B,CAAC,CAAC,QAAQ,EAAE,CAAC;YAElF,IAAI,WAAW,EAAE,CAAC;gBAChB,IAAI,IAAI,IAAI,WAAW,EAAE,CAAC;YAC5B,CAAC;YAED,OAAO,IAAA,qBAAa,EAAC,OAAO,CAAC,aAAa,CAAC,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;QAClE,CAAC;KACF,CAAC;AACJ,CAAC;AAED,SAAgB,eAAe,CAAC,IAAY,EAAE,MAAsB;IAClE,MAAM,MAAM,GAAsC,EAAE,CAAC;IAErD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC9B,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAE7C,KAAK,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,IAAI,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;QACxD,MAAM,WAAW,GAAG,IAAA,2BAAgB,EAAC,UAAU,CAAC,CAAC;QACjD,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,SAAS;QACX,CAAC;aAAM,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;YAC7B,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;YACxC,SAAS;QACX,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM;QACR,CAAC;IACH,CAAC;IAED,OAAO,sBAAsB,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;AAC5D,CAAC;AAED,SAAgB,sBAAsB,CAAC,IAAY,EAAE,MAAyC;IAC5F,OAAO,IAAI;SACR,KAAK,CAAC,GAAG,CAAC;SACV,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;QACZ,MAAM,WAAW,GAAG,IAAA,2BAAgB,EAAC,IAAI,CAAC,CAAC;QAC3C,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC;QACd,CAAC;aAAM,CAAC;YACN,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACvC,OAAO,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAChC,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC,CAAC;SACD,MAAM,CAAC,OAAO,CAAC;SACf,IAAI,CAAC,GAAG,CAAC,CAAC;AACf,CAAC", "sourcesContent": ["import * as Linking from 'expo-linking';\nimport { createElement } from 'react';\n\nimport { cleanPath } from './fork/getStateFromPath-forks';\nimport { RedirectConfig } from './getRoutesCore';\nimport type { StoreRedirects } from './global-state/router-store';\nimport { matchDynamicName } from './matchers';\n\nexport function applyRedirects(\n  url: string | null | undefined,\n  redirects: StoreRedirects[] | undefined\n): string | undefined | null {\n  if (typeof url !== 'string' || !redirects) {\n    return url;\n  }\n\n  const nextUrl = cleanPath(url);\n  const redirect = redirects.find(([regex]) => regex.test(nextUrl));\n\n  if (!redirect) {\n    return url;\n  }\n\n  // If the redirect is external, open the URL\n  if (redirect[2]) {\n    let href = redirect[1].destination;\n    if (href.startsWith('//') && process.env.EXPO_OS !== 'web') {\n      href = `https:${href}`;\n    }\n\n    Linking.openURL(href);\n    return href;\n  }\n\n  return applyRedirects(convertRedirect(url, redirect[1]), redirects);\n}\n\nexport function getRedirectModule(route: string) {\n  return {\n    default: function RedirectComponent() {\n      // Use the store directly instead of useGlobalSearchParams.\n      // Importing the hooks directly causes build errors on the server\n      const params = require('./hooks').useGlobalSearchParams();\n\n      // Replace dynamic parts of the route with the actual values from the params\n      let href = route\n        .split('/')\n        .map((part) => {\n          const dynamicName = matchDynamicName(part);\n          if (!dynamicName) {\n            return part;\n          } else {\n            const param = params[dynamicName.name];\n            delete params[dynamicName.name];\n            return param;\n          }\n        })\n        .filter(Boolean)\n        .join('/');\n\n      // Add any remaining params as query string\n      const queryString = new URLSearchParams(params as Record<string, any>).toString();\n\n      if (queryString) {\n        href += `?${queryString}`;\n      }\n\n      return createElement(require('./link/Link').Redirect, { href });\n    },\n  };\n}\n\nexport function convertRedirect(path: string, config: RedirectConfig) {\n  const params: Record<string, string | string[]> = {};\n\n  const parts = path.split('/');\n  const sourceParts = config.source.split('/');\n\n  for (const [index, sourcePart] of sourceParts.entries()) {\n    const dynamicName = matchDynamicName(sourcePart);\n    if (!dynamicName) {\n      continue;\n    } else if (!dynamicName.deep) {\n      params[dynamicName.name] = parts[index];\n      continue;\n    } else {\n      params[dynamicName.name] = parts.slice(index);\n      break;\n    }\n  }\n\n  return mergeVariablesWithPath(config.destination, params);\n}\n\nexport function mergeVariablesWithPath(path: string, params: Record<string, string | string[]>) {\n  return path\n    .split('/')\n    .map((part) => {\n      const dynamicName = matchDynamicName(part);\n      if (!dynamicName) {\n        return part;\n      } else {\n        const param = params[dynamicName.name];\n        delete params[dynamicName.name];\n        return param;\n      }\n    })\n    .filter(Boolean)\n    .join('/');\n}\n"]}