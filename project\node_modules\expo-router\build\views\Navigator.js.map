{"version": 3, "file": "Navigator.js", "sourceRoot": "", "sources": ["../../src/views/Navigator.tsx"], "names": [], "mappings": ";AAAA,mCAAmC;AACnC,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCb,8BAgDC;AAKD,kDAMC;AAgCD,oBAcC;AAmBD,4CAMC;AAzKD,qDAA+E;AAC/E,6CAA+B;AAC/B,iCAAiC;AACjC,+EAA4D;AAC5D,mFAA8D;AAE9D,qCAAkC;AAClC,oCAAyC;AACzC,wDAAqD;AACrD,oEAAuE;AACvE,8CAAiD;AAOpC,QAAA,gBAAgB,GAAG,KAAK,CAAC,aAAa,CAA+B,IAAI,CAAC,CAAC;AAExF,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;IAC1C,wBAAgB,CAAC,WAAW,GAAG,kBAAkB,CAAC;AACpD,CAAC;AAaD;;;;GAIG;AACH,SAAgB,SAAS,CAA4D,EACnF,gBAAgB,EAChB,aAAa,EACb,QAAQ,EACR,MAAM,EACN,aAAa,GACK;IAClB,MAAM,UAAU,GAAG,IAAA,qBAAa,GAAE,CAAC;IAEnC,+FAA+F;IAC/F,MAAM,EACJ,OAAO,EACP,QAAQ,EAAE,iBAAiB,EAC3B,gBAAgB,GACjB,GAAG,IAAA,2CAAuB,EAAC,QAAQ,EAAE;QACpC,iBAAiB,EAAE,IAAI;QACvB,UAAU;KACX,CAAC,CAAC;IAEH,MAAM,aAAa,GAAG,IAAA,6BAAgB,EAAC,OAAO,IAAI,EAAE,EAAE,gBAAgB,CAAC,CAAC;IAExE,MAAM,KAAK,yBAA2B,CAAC;IAEvC,MAAM,UAAU,GAAG,IAAA,6BAAoB,EAAC,MAAM,EAAE;QAC9C,4EAA4E;QAC5E,GAAG,aAAa;QAChB,EAAE,EAAE,UAAU;QACd,QAAQ,EAAE,aAAa,IAAI,CAAC,CAAC,eAAM,CAAC,GAAG,CAAC,SAAS,EAAG,CAAC;QACrD,aAAa;QACb,gBAAgB;KACjB,CAAC,CAAC;IAEH,2FAA2F;IAC3F,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;QAC1B,OAAO,CAAC,IAAI,CAAC,iBAAiB,UAAU,oBAAoB,CAAC,CAAC;QAC9D,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,CACL,CAAC,wBAAgB,CAAC,QAAQ,CACxB,KAAK,CAAC,CAAC;YACL,GAAG,UAAU;YACb,UAAU;YACV,MAAM;SACP,CAAC,CACF;MAAA,CAAC,iBAAiB,CACpB;IAAA,EAAE,wBAAgB,CAAC,QAAQ,CAAC,CAC7B,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,mBAAmB;IACjC,MAAM,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,wBAAgB,CAAC,CAAC;IAC5C,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;IAC7E,CAAC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,aAAa,CAAC,KAA0B;IAC/C,MAAM,UAAU,GAAG,IAAA,qBAAa,GAAE,CAAC;IAEnC,mEAAmE;IACnE,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,GAAG,IAAA,2CAAuB,EAAC,EAAE,EAAE;QAChE,UAAU;KACX,CAAC,CAAC;IAEH,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,iBAAiB,EAAE,GAAG,IAAA,6BAAoB,EAAC,yBAAW,EAAE;QAClF,GAAG,KAAK;QACR,EAAE,EAAE,UAAU;QACd,QAAQ,EAAE,IAAA,6BAAgB,EAAC,OAAO,IAAI,EAAE,EAAE,gBAAgB,CAAC;KAC5D,CAAC,CAAC;IAEH,OAAO,CACL,CAAC,iBAAiB,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,iBAAiB,CAAC,CAC7F,CAAC;AACJ,CAAC;AAED;;;;;;;;;;GAUG;AACH,SAAgB,IAAI,CAAC,KAA4C;IAC/D,MAAM,UAAU,GAAG,IAAA,qBAAa,GAAE,CAAC;IACnC,MAAM,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,wBAAgB,CAAC,CAAC;IAE5C,IAAI,OAAO,EAAE,UAAU,KAAK,UAAU,EAAE,CAAC;QACvC,mDAAmD;QACnD,OAAO,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,EAAG,CAAC;IACtC,CAAC;IAED;;;OAGG;IACH,OAAO,CAAC,aAAa,CAAC,AAAD,EAAG,CAAC;AAC3B,CAAC;AAED;;GAEG;AACH,SAAS,aAAa;IACpB,MAAM,OAAO,GAAG,mBAAmB,EAAE,CAAC;IAEtC,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;IAEvC,OAAO,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,IAAI,IAAI,CAAC;AACtE,CAAC;AAED,MAAM,oBAAoB,GACxB,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,SAAS,IAAI,IAAA,2CAAY,GAAE,CAAC,CAAC,CAAC,gBAAQ,CAAC,CAAC,CAAC,6CAAY,CAAC;AAEhF;;GAEG;AACH,SAAgB,gBAAgB;IAC9B,OAAO,CACL,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CACvC;MAAA,CAAC,aAAa,CAAC,AAAD,EAChB;IAAA,EAAE,oBAAoB,CAAC,CACxB,CAAC;AACJ,CAAC;AAED,SAAS,CAAC,IAAI,GAAG,aAAa,CAAC;AAC/B,SAAS,CAAC,UAAU,GAAG,mBAAmB,CAAC;AAE3C,wCAAwC;AACxC,SAAS,CAAC,MAAM,GAAG,eAAM,CAAC", "sourcesContent": ["// Copyright © 2024 650 Industries.\n'use client';\n\nimport { RouterFactory, useNavigationBuilder } from '@react-navigation/native';\nimport * as React from 'react';\nimport { Fragment } from 'react';\nimport { isEdgeToEdge } from 'react-native-is-edge-to-edge';\nimport { SafeAreaView } from 'react-native-safe-area-context';\n\nimport { Screen } from './Screen';\nimport { useContextKey } from '../Route';\nimport { StackRouter } from '../layouts/StackClient';\nimport { useFilterScreenChildren } from '../layouts/withLayoutContext';\nimport { useSortedScreens } from '../useScreens';\n\nexport type NavigatorContextValue = ReturnType<typeof useNavigationBuilder> & {\n  contextKey: string;\n  router: RouterFactory<any, any, any>;\n};\n\nexport const NavigatorContext = React.createContext<NavigatorContextValue | null>(null);\n\nif (process.env.NODE_ENV !== 'production') {\n  NavigatorContext.displayName = 'NavigatorContext';\n}\n\ntype UseNavigationBuilderRouter = Parameters<typeof useNavigationBuilder>[0];\ntype UseNavigationBuilderOptions = Parameters<typeof useNavigationBuilder>[1];\n\nexport type NavigatorProps<T extends UseNavigationBuilderRouter> = {\n  initialRouteName?: UseNavigationBuilderOptions['initialRouteName'];\n  screenOptions?: UseNavigationBuilderOptions['screenOptions'];\n  children?: UseNavigationBuilderOptions['children'];\n  router?: T;\n  routerOptions?: Omit<Parameters<T>[0], 'initialRouteName'>;\n};\n\n/**\n * An unstyled custom navigator. Good for basic web layouts.\n *\n * @hidden\n */\nexport function Navigator<T extends UseNavigationBuilderRouter = typeof StackRouter>({\n  initialRouteName,\n  screenOptions,\n  children,\n  router,\n  routerOptions,\n}: NavigatorProps<T>) {\n  const contextKey = useContextKey();\n\n  // A custom navigator can have a mix of Screen and other components (like a Slot inside a View)\n  const {\n    screens,\n    children: nonScreenChildren,\n    protectedScreens,\n  } = useFilterScreenChildren(children, {\n    isCustomNavigator: true,\n    contextKey,\n  });\n\n  const sortedScreens = useSortedScreens(screens ?? [], protectedScreens);\n\n  router ||= StackRouter as unknown as T;\n\n  const navigation = useNavigationBuilder(router, {\n    // Used for getting the parent with navigation.getParent('/normalized/path')\n    ...routerOptions,\n    id: contextKey,\n    children: sortedScreens || [<Screen key=\"default\" />],\n    screenOptions,\n    initialRouteName,\n  });\n\n  // useNavigationBuilder requires at least one screen to be defined otherwise it will throw.\n  if (!sortedScreens.length) {\n    console.warn(`Navigator at \"${contextKey}\" has no children.`);\n    return null;\n  }\n\n  return (\n    <NavigatorContext.Provider\n      value={{\n        ...navigation,\n        contextKey,\n        router,\n      }}>\n      {nonScreenChildren}\n    </NavigatorContext.Provider>\n  );\n}\n\n/**\n * @hidden\n */\nexport function useNavigatorContext() {\n  const context = React.use(NavigatorContext);\n  if (!context) {\n    throw new Error('useNavigatorContext must be used within a <Navigator />');\n  }\n  return context;\n}\n\nfunction SlotNavigator(props: NavigatorProps<any>) {\n  const contextKey = useContextKey();\n\n  // Allows adding Screen components as children to configure routes.\n  const { screens, protectedScreens } = useFilterScreenChildren([], {\n    contextKey,\n  });\n\n  const { state, descriptors, NavigationContent } = useNavigationBuilder(StackRouter, {\n    ...props,\n    id: contextKey,\n    children: useSortedScreens(screens ?? [], protectedScreens),\n  });\n\n  return (\n    <NavigationContent>{descriptors[state.routes[state.index].key].render()}</NavigationContent>\n  );\n}\n\n/**\n * Renders the currently selected content.\n *\n * There are actually two different implementations of `<Slot/>`:\n *  - Used inside a `_layout` as the `Navigator`\n *  - Used inside a `Navigator` as the content\n *\n * Since a custom `Navigator` will set the `NavigatorContext.contextKey` to\n * the current `_layout`, you can use this to determine if you are inside\n * a custom navigator or not.\n */\nexport function Slot(props: Omit<NavigatorProps<any>, 'children'>) {\n  const contextKey = useContextKey();\n  const context = React.use(NavigatorContext);\n\n  if (context?.contextKey !== contextKey) {\n    // The _layout has changed since the last navigator\n    return <SlotNavigator {...props} />;\n  }\n\n  /*\n   * The user has defined a custom navigator\n   * <Navigator><Slot /></Navigator>\n   */\n  return <NavigatorSlot />;\n}\n\n/**\n * Render the current navigator content.\n */\nfunction NavigatorSlot() {\n  const context = useNavigatorContext();\n\n  const { state, descriptors } = context;\n\n  return descriptors[state.routes[state.index].key]?.render() ?? null;\n}\n\nconst SlotNavigatorWrapper =\n  process.env.EXPO_OS === 'android' && isEdgeToEdge() ? Fragment : SafeAreaView;\n\n/**\n * The default navigator for the app when no root _layout is provided.\n */\nexport function DefaultNavigator() {\n  return (\n    <SlotNavigatorWrapper style={{ flex: 1 }}>\n      <SlotNavigator />\n    </SlotNavigatorWrapper>\n  );\n}\n\nNavigator.Slot = NavigatorSlot;\nNavigator.useContext = useNavigatorContext;\n\n/** Used to configure route settings. */\nNavigator.Screen = Screen;\n"]}